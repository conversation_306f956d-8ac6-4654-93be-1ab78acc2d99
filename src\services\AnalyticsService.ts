import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Dimensions } from 'react-native';
import AdvancedDataManager from './AdvancedDataManager';
import SecurityManager from './SecurityManager';

export class AnalyticsService {
  private static instance: AnalyticsService;
  private dataManager: AdvancedDataManager;
  private securityManager: SecurityManager;
  private sessionId: string | null = null;
  private sessionStartTime: number = 0;
  private eventQueue: AnalyticsEvent[] = [];
  private isInitialized: boolean = false;
  private userProperties: UserProperties = {};

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  constructor() {
    this.dataManager = AdvancedDataManager.getInstance();
    this.securityManager = SecurityManager.getInstance();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      await this.securityManager.initialize();
      
      // Start new session
      await this.startSession();
      
      // Load user properties
      this.userProperties = await this.dataManager.getItem<UserProperties>('user_properties') || {};
      
      // Setup periodic flush
      this.setupPeriodicFlush();
      
      // Track app launch
      this.trackEvent('app_launched', {
        platform: Platform.OS,
        version: Platform.Version,
        screen_width: Dimensions.get('window').width,
        screen_height: Dimensions.get('window').height,
      });
      
      this.isInitialized = true;
      console.log('📊 Analytics Service initialized successfully');
    } catch (error) {
      console.error('❌ Analytics Service initialization failed:', error);
    }
  }

  // Session management
  private async startSession(): Promise<void> {
    this.sessionId = await this.securityManager.createSecureSession('analytics');
    this.sessionStartTime = Date.now();
    
    console.log(`📊 Analytics session started: ${this.sessionId}`);
  }

  async endSession(): Promise<void> {
    if (!this.sessionId) return;
    
    const sessionDuration = Date.now() - this.sessionStartTime;
    
    this.trackEvent('session_ended', {
      session_duration: sessionDuration,
      events_tracked: this.eventQueue.length,
    });
    
    await this.flushEvents();
    this.sessionId = null;
  }

  // Event tracking
  trackEvent(eventName: string, properties: Record<string, any> = {}): void {
    if (!this.isInitialized) {
      console.warn('⚠️ Analytics not initialized, queuing event:', eventName);
    }

    const event: AnalyticsEvent = {
      id: this.generateEventId(),
      name: eventName,
      properties: {
        ...properties,
        timestamp: Date.now(),
        session_id: this.sessionId,
        platform: Platform.OS,
        app_version: '1.0.0',
      },
      timestamp: Date.now(),
    };

    this.eventQueue.push(event);
    
    // Auto-flush if queue is getting large
    if (this.eventQueue.length >= 50) {
      this.flushEvents();
    }
    
    console.log(`📊 Event tracked: ${eventName}`, properties);
  }

  // Screen tracking
  trackScreen(screenName: string, properties: Record<string, any> = {}): void {
    this.trackEvent('screen_view', {
      screen_name: screenName,
      ...properties,
    });
  }

  // User interaction tracking
  trackUserInteraction(interactionType: string, element: string, properties: Record<string, any> = {}): void {
    this.trackEvent('user_interaction', {
      interaction_type: interactionType,
      element,
      ...properties,
    });
  }

  // Performance tracking
  trackPerformance(operation: string, duration: number, properties: Record<string, any> = {}): void {
    this.trackEvent('performance_metric', {
      operation,
      duration,
      ...properties,
    });
  }

  // Error tracking
  trackError(error: Error, context: string, properties: Record<string, any> = {}): void {
    this.trackEvent('error_occurred', {
      error_message: error.message,
      error_stack: error.stack?.substring(0, 500), // Truncate stack trace
      context,
      ...properties,
    });
  }

  // Business metrics
  trackBusinessMetric(metricName: string, value: number, properties: Record<string, any> = {}): void {
    this.trackEvent('business_metric', {
      metric_name: metricName,
      value,
      ...properties,
    });
  }

  // User properties
  async setUserProperty(key: string, value: any): Promise<void> {
    this.userProperties[key] = value;
    await this.dataManager.setItem('user_properties', this.userProperties);
    
    this.trackEvent('user_property_set', {
      property_key: key,
      property_value: typeof value === 'object' ? JSON.stringify(value) : value,
    });
  }

  async setUserProperties(properties: Record<string, any>): Promise<void> {
    this.userProperties = { ...this.userProperties, ...properties };
    await this.dataManager.setItem('user_properties', this.userProperties);
    
    this.trackEvent('user_properties_batch_set', {
      properties_count: Object.keys(properties).length,
    });
  }

  // Conversion tracking
  trackConversion(conversionType: string, value?: number, properties: Record<string, any> = {}): void {
    this.trackEvent('conversion', {
      conversion_type: conversionType,
      value,
      ...properties,
    });
  }

  // A/B testing support
  trackExperiment(experimentName: string, variant: string, properties: Record<string, any> = {}): void {
    this.trackEvent('experiment_exposure', {
      experiment_name: experimentName,
      variant,
      ...properties,
    });
  }

  // Custom funnel tracking
  trackFunnelStep(funnelName: string, step: string, properties: Record<string, any> = {}): void {
    this.trackEvent('funnel_step', {
      funnel_name: funnelName,
      step,
      ...properties,
    });
  }

  // Event flushing
  private async flushEvents(): Promise<void> {
    if (this.eventQueue.length === 0) return;
    
    const eventsToFlush = [...this.eventQueue];
    this.eventQueue = [];
    
    try {
      // Store events locally
      await this.storeEventsLocally(eventsToFlush);
      
      // In production, send to analytics service
      // await this.sendEventsToServer(eventsToFlush);
      
      console.log(`📊 Flushed ${eventsToFlush.length} analytics events`);
    } catch (error) {
      console.error('❌ Failed to flush analytics events:', error);
      // Re-queue events on failure
      this.eventQueue.unshift(...eventsToFlush);
    }
  }

  private async storeEventsLocally(events: AnalyticsEvent[]): Promise<void> {
    const existingEvents = await this.dataManager.getItem<AnalyticsEvent[]>('analytics_events') || [];
    const allEvents = [...existingEvents, ...events];
    
    // Keep only last 1000 events to manage storage
    const recentEvents = allEvents.slice(-1000);
    
    await this.dataManager.setItem('analytics_events', recentEvents, {
      compress: true,
      syncToServer: true,
      priority: 'low',
    });
  }

  // Analytics insights
  async getAnalyticsInsights(): Promise<AnalyticsInsights> {
    const events = await this.dataManager.getItem<AnalyticsEvent[]>('analytics_events') || [];
    
    const insights: AnalyticsInsights = {
      totalEvents: events.length,
      uniqueSessions: new Set(events.map(e => e.properties.session_id)).size,
      topEvents: this.getTopEvents(events),
      screenViews: this.getScreenViewStats(events),
      errorRate: this.calculateErrorRate(events),
      averageSessionDuration: this.calculateAverageSessionDuration(events),
      userEngagement: this.calculateUserEngagement(events),
    };
    
    return insights;
  }

  private getTopEvents(events: AnalyticsEvent[]): Array<{ name: string; count: number }> {
    const eventCounts: Record<string, number> = {};
    
    events.forEach(event => {
      eventCounts[event.name] = (eventCounts[event.name] || 0) + 1;
    });
    
    return Object.entries(eventCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([name, count]) => ({ name, count }));
  }

  private getScreenViewStats(events: AnalyticsEvent[]): Array<{ screen: string; views: number }> {
    const screenViews = events.filter(e => e.name === 'screen_view');
    const screenCounts: Record<string, number> = {};
    
    screenViews.forEach(event => {
      const screenName = event.properties.screen_name;
      screenCounts[screenName] = (screenCounts[screenName] || 0) + 1;
    });
    
    return Object.entries(screenCounts)
      .sort(([, a], [, b]) => b - a)
      .map(([screen, views]) => ({ screen, views }));
  }

  private calculateErrorRate(events: AnalyticsEvent[]): number {
    const totalEvents = events.length;
    const errorEvents = events.filter(e => e.name === 'error_occurred').length;
    
    return totalEvents > 0 ? (errorEvents / totalEvents) * 100 : 0;
  }

  private calculateAverageSessionDuration(events: AnalyticsEvent[]): number {
    const sessionEndEvents = events.filter(e => e.name === 'session_ended');
    
    if (sessionEndEvents.length === 0) return 0;
    
    const totalDuration = sessionEndEvents.reduce((sum, event) => {
      return sum + (event.properties.session_duration || 0);
    }, 0);
    
    return totalDuration / sessionEndEvents.length;
  }

  private calculateUserEngagement(events: AnalyticsEvent[]): UserEngagement {
    const interactionEvents = events.filter(e => e.name === 'user_interaction');
    const screenViews = events.filter(e => e.name === 'screen_view');
    
    return {
      totalInteractions: interactionEvents.length,
      screenViews: screenViews.length,
      interactionsPerSession: interactionEvents.length / Math.max(1, new Set(events.map(e => e.properties.session_id)).size),
    };
  }

  // Utility methods
  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupPeriodicFlush(): void {
    // Flush events every 30 seconds
    setInterval(() => {
      this.flushEvents();
    }, 30000);
  }

  // Real-time analytics dashboard data
  async getDashboardData(): Promise<DashboardData> {
    const insights = await this.getAnalyticsInsights();
    const recentEvents = await this.getRecentEvents(100);
    
    return {
      insights,
      recentEvents,
      realTimeMetrics: {
        activeUsers: 1, // Current user
        eventsPerMinute: this.calculateEventsPerMinute(recentEvents),
        errorRate: insights.errorRate,
      },
    };
  }

  private async getRecentEvents(limit: number): Promise<AnalyticsEvent[]> {
    const events = await this.dataManager.getItem<AnalyticsEvent[]>('analytics_events') || [];
    return events.slice(-limit);
  }

  private calculateEventsPerMinute(events: AnalyticsEvent[]): number {
    const oneMinuteAgo = Date.now() - 60000;
    const recentEvents = events.filter(e => e.timestamp > oneMinuteAgo);
    return recentEvents.length;
  }

  // Export analytics data
  async exportAnalyticsData(): Promise<string> {
    const events = await this.dataManager.getItem<AnalyticsEvent[]>('analytics_events') || [];
    const insights = await this.getAnalyticsInsights();
    
    const exportData = {
      events,
      insights,
      userProperties: this.userProperties,
      exportTimestamp: Date.now(),
    };
    
    return JSON.stringify(exportData, null, 2);
  }
}

// Type definitions
interface AnalyticsEvent {
  id: string;
  name: string;
  properties: Record<string, any>;
  timestamp: number;
}

interface UserProperties {
  [key: string]: any;
}

interface AnalyticsInsights {
  totalEvents: number;
  uniqueSessions: number;
  topEvents: Array<{ name: string; count: number }>;
  screenViews: Array<{ screen: string; views: number }>;
  errorRate: number;
  averageSessionDuration: number;
  userEngagement: UserEngagement;
}

interface UserEngagement {
  totalInteractions: number;
  screenViews: number;
  interactionsPerSession: number;
}

interface DashboardData {
  insights: AnalyticsInsights;
  recentEvents: AnalyticsEvent[];
  realTimeMetrics: {
    activeUsers: number;
    eventsPerMinute: number;
    errorRate: number;
  };
}

export default AnalyticsService;
