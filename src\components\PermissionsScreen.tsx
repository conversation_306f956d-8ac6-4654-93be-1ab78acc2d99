import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
  ScrollView,
  Platform,
  PermissionsAndroid,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import * as Location from 'expo-location';
import {
  Pedometer,
  Barometer,
  LightSensor,
  Gyroscope,
  Accelerometer,
  Magnetometer
} from 'expo-sensors';
import {
  request,
  check,
  PERMISSIONS,
  RESULTS,
  requestMultiple,
  openSettings
} from 'react-native-permissions';
import NotificationService from '../services/NotificationService';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  ZoomIn,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import NotificationService from '../services/NotificationService';

interface PermissionsScreenProps {
  onPermissionsGranted: () => void;
}

interface Permission {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  granted: boolean;
  requestFunction: () => Promise<boolean>;
}

const PermissionsScreen: React.FC<PermissionsScreenProps> = ({ onPermissionsGranted }) => {
  const [permissions, setPermissions] = useState<Permission[]>([
    {
      id: 'camera',
      title: 'Camera Access',
      description: 'Scan food items and take photos for meal tracking',
      icon: 'camera',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'microphone',
      title: 'Microphone Access',
      description: 'Voice commands and audio input for AI assistant',
      icon: 'mic',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        const { status } = await Audio.requestPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'photos',
      title: 'Photo Library',
      description: 'Select photos from your gallery for meal analysis',
      icon: 'images',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'location',
      title: 'Location Access',
      description: 'Find nearby restaurants and local nutrition data',
      icon: 'location',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        const { status } = await Location.requestForegroundPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'sensors',
      title: 'Body Sensors',
      description: 'Track heart rate and fitness data for health monitoring',
      icon: 'fitness',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        try {
          console.log('🔍 Requesting body sensors permissions using latest Android methods...');

          if (Platform.OS === 'android') {
            // Use react-native-permissions for latest Android permission handling
            const permissionsToRequest = [];

            // Add body sensors permission
            if (PERMISSIONS.ANDROID.BODY_SENSORS) {
              permissionsToRequest.push(PERMISSIONS.ANDROID.BODY_SENSORS);
            }

            // Add activity recognition for Android 10+ (API 29+)
            if (Platform.Version >= 29 && PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION) {
              permissionsToRequest.push(PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION);
            }

            // Add background body sensors for Android 14+ (API 34+)
            if (Platform.Version >= 34 && PERMISSIONS.ANDROID.BODY_SENSORS_BACKGROUND) {
              permissionsToRequest.push(PERMISSIONS.ANDROID.BODY_SENSORS_BACKGROUND);
            }

            console.log('📱 Requesting permissions:', permissionsToRequest);

            if (permissionsToRequest.length > 0) {
              // Check current status first
              const currentStatuses = await Promise.all(
                permissionsToRequest.map(permission => check(permission))
              );

              console.log('📱 Current permission statuses:', currentStatuses);

              // Request permissions that aren't already granted
              const results = await requestMultiple(permissionsToRequest);
              console.log('📱 Permission request results:', results);

              // Check if at least one critical permission was granted
              const bodySensorsGranted = results[PERMISSIONS.ANDROID.BODY_SENSORS] === RESULTS.GRANTED;
              const activityGranted = results[PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION] === RESULTS.GRANTED;

              if (!bodySensorsGranted && !activityGranted) {
                console.log('❌ Critical sensor permissions denied');

                // Show dialog to go to settings
                Alert.alert(
                  'Permissions Required',
                  'Body sensors and activity recognition permissions are required for health monitoring. Please enable them in Settings.',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Open Settings',
                      onPress: () => openSettings().catch(() => Linking.openSettings())
                    }
                  ]
                );
                return false;
              }
            }
          }

          // Test Expo sensors availability and request permissions
          console.log('🔍 Testing Expo sensor availability...');

          const sensorTests = await Promise.all([
            Pedometer.isAvailableAsync().catch(() => false),
            Accelerometer.isAvailableAsync().catch(() => false),
            Gyroscope.isAvailableAsync().catch(() => false),
            Magnetometer.isAvailableAsync().catch(() => false),
            Barometer.isAvailableAsync().catch(() => false),
            LightSensor.isAvailableAsync().catch(() => false)
          ]);

          console.log('📱 Expo sensor availability:', {
            pedometer: sensorTests[0],
            accelerometer: sensorTests[1],
            gyroscope: sensorTests[2],
            magnetometer: sensorTests[3],
            barometer: sensorTests[4],
            lightSensor: sensorTests[5]
          });

          // Request Expo sensor permissions for available sensors
          const sensorPermissionPromises = [];

          if (sensorTests[0]) { // Pedometer
            sensorPermissionPromises.push(
              Pedometer.requestPermissionsAsync().catch(error => {
                console.log('Pedometer permission error:', error);
                return { status: 'denied' };
              })
            );
          }

          if (sensorTests[1]) { // Accelerometer
            sensorPermissionPromises.push(
              Accelerometer.requestPermissionsAsync().catch(error => {
                console.log('Accelerometer permission error:', error);
                return { status: 'denied' };
              })
            );
          }

          if (sensorTests[2]) { // Gyroscope
            sensorPermissionPromises.push(
              Gyroscope.requestPermissionsAsync().catch(error => {
                console.log('Gyroscope permission error:', error);
                return { status: 'denied' };
              })
            );
          }

          if (sensorPermissionPromises.length > 0) {
            const sensorResults = await Promise.all(sensorPermissionPromises);
            console.log('📱 Expo sensor permission results:', sensorResults);

            const hasAnySensorPermission = sensorResults.some(result => result.status === 'granted');

            if (hasAnySensorPermission) {
              console.log('✅ Body sensors permissions granted successfully');
              return true;
            }
          }

          console.log('⚠️ Limited sensor access - some health features may not work optimally');
          return true; // Return true even with limited access to not block the app

        } catch (error) {
          console.error('❌ Error requesting body sensors permissions:', error);
          return false;
        }
      },
    },
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'Receive meal reminders and achievement notifications',
      icon: 'notifications',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        try {
          console.log('🔍 Requesting notifications permission using latest methods...');

          // Use the NotificationService which handles the latest notification permission methods
          const granted = await NotificationService.requestPermissions();
          console.log('📱 Notifications permission result:', granted);

          if (!granted) {
            // Show dialog to go to settings if permission was denied
            Alert.alert(
              'Notification Permission Required',
              'Notifications are required for meal reminders and health alerts. Please enable them in Settings.',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Open Settings',
                  onPress: () => openSettings().catch(() => Linking.openSettings())
                }
              ]
            );
          }

          return granted;
        } catch (error) {
          console.error('❌ Error requesting notifications permission:', error);
          return false;
        }
      },
    },
    {
      id: 'activity',
      title: 'Physical Activity',
      description: 'Monitor steps, running, and daily activity patterns',
      icon: 'walk',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        try {
          console.log('🔍 Requesting physical activity permissions using latest methods...');

          if (Platform.OS === 'android') {
            // Use react-native-permissions for latest Android permission handling
            const permissionsToRequest = [];

            // Activity recognition is required for Android 10+ (API 29+)
            if (Platform.Version >= 29 && PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION) {
              permissionsToRequest.push(PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION);
            }

            // Add step counter permission if available (some devices)
            if (PERMISSIONS.ANDROID.BODY_SENSORS) {
              permissionsToRequest.push(PERMISSIONS.ANDROID.BODY_SENSORS);
            }

            console.log('📱 Requesting activity permissions:', permissionsToRequest);

            if (permissionsToRequest.length > 0) {
              // Check current status first
              const currentStatuses = await Promise.all(
                permissionsToRequest.map(permission => check(permission))
              );

              console.log('📱 Current activity permission statuses:', currentStatuses);

              // Request permissions that aren't already granted
              const results = await requestMultiple(permissionsToRequest);
              console.log('📱 Activity permission request results:', results);

              // Check if activity recognition was granted (critical for step counting)
              const activityGranted = results[PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION] === RESULTS.GRANTED;
              const bodySensorsGranted = results[PERMISSIONS.ANDROID.BODY_SENSORS] === RESULTS.GRANTED;

              if (!activityGranted && Platform.Version >= 29) {
                console.log('❌ Activity recognition permission denied');

                // Show dialog to go to settings
                Alert.alert(
                  'Activity Permission Required',
                  'Activity recognition permission is required for step counting and fitness tracking. Please enable it in Settings.',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Open Settings',
                      onPress: () => openSettings().catch(() => Linking.openSettings())
                    }
                  ]
                );
                return false;
              }

              if (!bodySensorsGranted && !activityGranted) {
                console.log('❌ No activity-related permissions granted');
                return false;
              }
            }
          }

          // Test Expo Pedometer availability and request permission
          console.log('🔍 Testing Expo Pedometer...');

          try {
            const isPedometerAvailable = await Pedometer.isAvailableAsync();
            console.log('📱 Pedometer available:', isPedometerAvailable);

            if (isPedometerAvailable) {
              const pedometerPermission = await Pedometer.requestPermissionsAsync();
              console.log('📱 Pedometer permission result:', pedometerPermission);

              if (pedometerPermission.status === 'granted') {
                console.log('✅ Physical activity permissions granted successfully');
                return true;
              } else {
                console.log('⚠️ Pedometer permission denied, but Android permissions may still work');
              }
            } else {
              console.log('⚠️ Pedometer not available on this device');
            }
          } catch (pedometerError) {
            console.log('⚠️ Pedometer error:', pedometerError);
          }

          // Test if we can get step data even without explicit Expo permission
          try {
            console.log('🔍 Testing step data access...');
            const testSteps = await Pedometer.getStepCountAsync(
              new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
              new Date()
            );
            console.log('📱 Test step data:', testSteps);

            if (testSteps.steps >= 0) {
              console.log('✅ Step data accessible - physical activity tracking enabled');
              return true;
            }
          } catch (stepTestError) {
            console.log('⚠️ Step data test failed:', stepTestError);
          }

          console.log('⚠️ Limited physical activity access - basic tracking may still work');
          return true; // Return true to not block the app, even with limited access

        } catch (error) {
          console.error('❌ Error requesting physical activity permissions:', error);
          return false;
        }
      },
    },
  ]);

  const [allGranted, setAllGranted] = useState(false);

  useEffect(() => {
    checkAllPermissions();
  }, []);

  const checkAllPermissions = async () => {
    const updatedPermissions = await Promise.all(
      permissions.map(async (permission) => {
        let granted = false;
        try {
          switch (permission.id) {
            case 'camera':
              const cameraStatus = await ImagePicker.getCameraPermissionsAsync();
              granted = cameraStatus.status === 'granted';
              break;
            case 'microphone':
              const audioStatus = await Audio.getPermissionsAsync();
              granted = audioStatus.status === 'granted';
              break;
            case 'photos':
              const mediaStatus = await ImagePicker.getMediaLibraryPermissionsAsync();
              granted = mediaStatus.status === 'granted';
              break;
            case 'location':
              const locationStatus = await Location.getForegroundPermissionsAsync();
              granted = locationStatus.status === 'granted';
              break;
            case 'sensors':
            case 'activity':
              const isAvailable = await Pedometer.isAvailableAsync();
              if (isAvailable) {
                const pedometerStatus = await Pedometer.getPermissionsAsync();
                granted = pedometerStatus.status === 'granted';
              } else {
                granted = false;
              }
              break;
          }
        } catch (error) {
          console.log(`Error checking ${permission.id} permission:`, error);
        }
        return { ...permission, granted };
      })
    );

    setPermissions(updatedPermissions);
    const allPermissionsGranted = updatedPermissions.every(p => p.granted);
    setAllGranted(allPermissionsGranted);
  };

  const requestPermission = async (permission: Permission) => {
    try {
      console.log(`🔍 User requesting ${permission.title} permission...`);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      const granted = await permission.requestFunction();
      console.log(`📱 ${permission.title} permission result: ${granted ? 'GRANTED' : 'DENIED'}`);

      setPermissions(prev =>
        prev.map(p =>
          p.id === permission.id ? { ...p, granted } : p
        )
      );

      if (!granted) {
        console.log(`⚠️ ${permission.title} permission denied by user`);
        Alert.alert(
          'Permission Required',
          `${permission.title} is needed for the best app experience. You can enable it later in Settings.`,
          [{ text: 'OK' }]
        );
      } else {
        console.log(`✅ ${permission.title} permission granted successfully`);
      }

      // Check if all permissions are now granted
      const updatedPermissions = permissions.map(p =>
        p.id === permission.id ? { ...p, granted } : p
      );
      const allPermissionsGranted = updatedPermissions.every(p => p.granted);
      setAllGranted(allPermissionsGranted);

      console.log(`📊 All permissions status: ${allPermissionsGranted ? 'ALL GRANTED' : 'SOME PENDING'}`);
    } catch (error) {
      console.error(`❌ Error requesting ${permission.title}:`, error);
      Alert.alert('Error', `Failed to request ${permission.title}. Please try again.`);
    }
  };

  const handleContinue = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    onPermissionsGranted();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
          <Animated.View entering={ZoomIn.delay(400).duration(600)} style={styles.iconContainer}>
            <Ionicons name="shield-checkmark" size={48} color="#6B7C5A" />
          </Animated.View>
          <Text style={styles.title}>App Permissions</Text>
          <Text style={styles.subtitle}>
            Grant permissions to unlock all features and get the best experience
          </Text>
        </Animated.View>

        {/* Permissions List */}
        <View style={styles.permissionsList}>
          {permissions.map((permission, index) => (
            <Animated.View
              key={permission.id}
              entering={SlideInLeft.delay(600 + index * 150).duration(600)}
              style={styles.permissionCard}
            >
              <View style={styles.permissionContent}>
                <View style={[styles.permissionIcon, { backgroundColor: `${permission.color}15` }]}>
                  <Ionicons name={permission.icon} size={24} color={permission.color} />
                </View>

                <View style={styles.permissionText}>
                  <Text style={styles.permissionTitle}>{permission.title}</Text>
                  <Text style={styles.permissionDescription}>{permission.description}</Text>
                </View>

                <TouchableOpacity
                  style={[
                    styles.permissionButton,
                    permission.granted && styles.permissionButtonGranted
                  ]}
                  onPress={() => !permission.granted && requestPermission(permission)}
                  disabled={permission.granted}
                >
                  <Ionicons
                    name={permission.granted ? 'checkmark' : 'add'}
                    size={20}
                    color={permission.granted ? '#FFFFFF' : '#6B7C5A'}
                  />
                </TouchableOpacity>
              </View>
            </Animated.View>
          ))}
        </View>

        {/* Continue Button */}
        <Animated.View entering={FadeInUp.delay(1200).duration(600)} style={styles.continueContainer}>
          <TouchableOpacity
            style={[styles.continueButton, allGranted && styles.continueButtonEnabled]}
            onPress={handleContinue}
          >
            <Text style={[styles.continueText, allGranted && styles.continueTextEnabled]}>
              {allGranted ? 'Continue to App' : 'Continue Anyway'}
            </Text>
            <Ionicons
              name="arrow-forward"
              size={20}
              color={allGranted ? '#FFFFFF' : '#6B7C5A'}
            />
          </TouchableOpacity>

          {!allGranted && (
            <Text style={styles.skipText}>
              You can grant permissions later in Settings
            </Text>
          )}
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 80,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: '800',
    color: '#6B7C5A',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 24,
  },
  permissionsList: {
    gap: 16,
    marginBottom: 32,
  },
  permissionCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  permissionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  permissionText: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    lineHeight: 20,
  },
  permissionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  permissionButtonGranted: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  continueContainer: {
    paddingTop: 24,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 32,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderWidth: 2,
    borderColor: '#6B7C5A',
    gap: 12,
  },
  continueButtonEnabled: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  continueText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B7C5A',
    letterSpacing: 0.5,
  },
  continueTextEnabled: {
    color: '#FFFFFF',
  },
  skipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    marginTop: 16,
  },
});

export default PermissionsScreen;
