import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  StatusBar,
  ScrollView,
  Platform,
  PermissionsAndroid,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import * as Location from 'expo-location';
import {
  Pedometer,
  Barometer,
  LightSensor,
  Gyroscope,
  Accelerometer,
  Magnetometer
} from 'expo-sensors';
// Removed react-native-permissions due to build issues
// Using only native Android methods and Expo APIs
import NotificationService from '../services/NotificationService';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  ZoomIn,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

interface PermissionsScreenProps {
  onPermissionsGranted: () => void;
}

interface Permission {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  granted: boolean;
  requestFunction: () => Promise<boolean>;
}

const PermissionsScreen: React.FC<PermissionsScreenProps> = ({ onPermissionsGranted }) => {
  const [permissions, setPermissions] = useState<Permission[]>([
    {
      id: 'camera',
      title: 'Camera Access',
      description: 'Scan food items and take photos for meal tracking',
      icon: 'camera',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'microphone',
      title: 'Microphone Access',
      description: 'Voice commands and audio input for AI assistant',
      icon: 'mic',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        const { status } = await Audio.requestPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'photos',
      title: 'Photo Library',
      description: 'Select photos from your gallery for meal analysis',
      icon: 'images',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'location',
      title: 'Location Access',
      description: 'Find nearby restaurants and local nutrition data',
      icon: 'location',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        const { status } = await Location.requestForegroundPermissionsAsync();
        return status === 'granted';
      },
    },
    {
      id: 'sensors',
      title: 'Body Sensors',
      description: 'Track heart rate and fitness data for health monitoring',
      icon: 'fitness',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        try {
          console.log('🔍 Requesting body sensors permissions using NATIVE Android methods...');
          console.log(`📱 Android API Level: ${Platform.Version}`);

          if (Platform.OS === 'android') {
            let hasAnyPermission = false;

            // METHOD 1: Try native PermissionsAndroid (primary method)
            try {
              console.log('🔄 Attempting Method 1: Native PermissionsAndroid...');
              const nativePermissions = [];

              // Add permissions based on Android version
              if (Platform.Version >= 34) {
                // Android 14+ permissions
                nativePermissions.push(
                  PermissionsAndroid.PERMISSIONS.BODY_SENSORS
                );

                // Try background body sensors if available
                if (PermissionsAndroid.PERMISSIONS.BODY_SENSORS_BACKGROUND) {
                  nativePermissions.push(PermissionsAndroid.PERMISSIONS.BODY_SENSORS_BACKGROUND);
                }
              } else if (Platform.Version >= 29) {
                // Android 10+ permissions
                nativePermissions.push(
                  PermissionsAndroid.PERMISSIONS.BODY_SENSORS,
                  PermissionsAndroid.PERMISSIONS.ACTIVITY_RECOGNITION
                );
              } else {
                // Android 9 and below
                nativePermissions.push(PermissionsAndroid.PERMISSIONS.BODY_SENSORS);
              }

              if (nativePermissions.length > 0) {
                const nativeResults = await PermissionsAndroid.requestMultiple(nativePermissions);
                console.log('📱 Method 1 results:', nativeResults);

                hasAnyPermission = Object.values(nativeResults).some(
                  result => result === PermissionsAndroid.RESULTS.GRANTED
                );

                if (hasAnyPermission) {
                  console.log('✅ Method 1 successful');
                  return true;
                }
              }
            } catch (method1Error) {
              console.log('⚠️ Method 1 failed:', method1Error);
            }

            // METHOD 2: Try individual permission requests (maximum compatibility)
            try {
              console.log('🔄 Attempting Method 2: Individual permission requests...');

              const individualPermissions = [];

              // Add permissions based on Android version
              if (Platform.Version >= 29) {
                individualPermissions.push({
                  permission: PermissionsAndroid.PERMISSIONS.ACTIVITY_RECOGNITION,
                  title: 'Activity Recognition Permission',
                  message: 'NutriAI needs activity recognition for step counting and fitness tracking.'
                });
              }

              individualPermissions.push({
                permission: PermissionsAndroid.PERMISSIONS.BODY_SENSORS,
                title: 'Body Sensors Permission',
                message: 'NutriAI needs body sensors for health monitoring and personalized nutrition.'
              });

              for (const permissionConfig of individualPermissions) {
                try {
                  const result = await PermissionsAndroid.request(
                    permissionConfig.permission,
                    {
                      title: permissionConfig.title,
                      message: permissionConfig.message,
                      buttonNeutral: 'Ask Me Later',
                      buttonNegative: 'Cancel',
                      buttonPositive: 'Allow',
                    }
                  );

                  if (result === PermissionsAndroid.RESULTS.GRANTED) {
                    console.log(`✅ Individual permission granted: ${permissionConfig.permission}`);
                    hasAnyPermission = true;
                  }
                } catch (individualError) {
                  console.log(`⚠️ Individual permission failed: ${permissionConfig.permission}`, individualError);
                }
              }

              if (hasAnyPermission) {
                console.log('✅ Method 2 successful');
                return true;
              }
            } catch (method2Error) {
              console.log('⚠️ Method 2 failed:', method2Error);
            }

            console.log('📱 Requesting permissions:', permissionsToRequest);

            if (permissionsToRequest.length > 0) {
              // Check current status first
              const currentStatuses = await Promise.all(
                permissionsToRequest.map(permission => check(permission))
              );

              // METHOD 3: Try Expo sensors as final fallback
            try {
              console.log('🔄 Attempting Method 3: Expo sensor permissions...');

              const sensorTests = await Promise.all([
                Pedometer.isAvailableAsync().catch(() => false),
                Accelerometer.isAvailableAsync().catch(() => false),
                Gyroscope.isAvailableAsync().catch(() => false),
                Magnetometer.isAvailableAsync().catch(() => false),
                Barometer.isAvailableAsync().catch(() => false),
                LightSensor.isAvailableAsync().catch(() => false)
              ]);

              console.log('📱 Expo sensor availability:', {
                pedometer: sensorTests[0],
                accelerometer: sensorTests[1],
                gyroscope: sensorTests[2],
                magnetometer: sensorTests[3],
                barometer: sensorTests[4],
                lightSensor: sensorTests[5]
              });

              // Request Expo sensor permissions for available sensors
              const sensorPermissionPromises = [];

              if (sensorTests[0]) { // Pedometer
                sensorPermissionPromises.push(
                  Pedometer.requestPermissionsAsync().catch(error => {
                    console.log('Pedometer permission error:', error);
                    return { status: 'denied' };
                  })
                );
              }

              if (sensorTests[1]) { // Accelerometer
                sensorPermissionPromises.push(
                  Accelerometer.requestPermissionsAsync().catch(error => {
                    console.log('Accelerometer permission error:', error);
                    return { status: 'denied' };
                  })
                );
              }

              if (sensorTests[2]) { // Gyroscope
                sensorPermissionPromises.push(
                  Gyroscope.requestPermissionsAsync().catch(error => {
                    console.log('Gyroscope permission error:', error);
                    return { status: 'denied' };
                  })
                );
              }

              if (sensorPermissionPromises.length > 0) {
                const sensorResults = await Promise.all(sensorPermissionPromises);
                console.log('📱 Method 3 results:', sensorResults);

                hasAnyPermission = sensorResults.some(result => result.status === 'granted');

                if (hasAnyPermission) {
                  console.log('✅ Method 3 successful');
                  return true;
                }
              }
            } catch (method3Error) {
              console.log('⚠️ Method 3 failed:', method3Error);
            }

            // If all methods failed, show comprehensive error
            if (!hasAnyPermission) {
              console.log('❌ All permission methods failed');

              Alert.alert(
                'Health Sensors Required',
                `Health sensor permissions are required for personalized nutrition tracking. Please enable them in Settings.\n\nAndroid ${Platform.Version} detected - tried 3 different permission methods.`,
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Open Settings',
                    onPress: () => Linking.openSettings()
                  }
                ]
              );
              return false;
            }
          }

          console.log('✅ Body sensors permissions granted successfully using native Android methods');
          return true;

        } catch (error) {
          console.error('❌ Error requesting body sensors permissions:', error);
          return false;
        }
      },
    },
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'Receive meal reminders and achievement notifications',
      icon: 'notifications',
      color: '#6B7C5A',
      granted: false,
      requestFunction: async () => {
        try {
          console.log('🔍 Requesting notifications permission using latest methods...');

          // Use the NotificationService which handles the latest notification permission methods
          const granted = await NotificationService.requestPermissions();
          console.log('📱 Notifications permission result:', granted);

          if (!granted) {
            // Show dialog to go to settings if permission was denied
            Alert.alert(
              'Notification Permission Required',
              'Notifications are required for meal reminders and health alerts. Please enable them in Settings.',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Open Settings',
                  onPress: () => openSettings().catch(() => Linking.openSettings())
                }
              ]
            );
          }

          return granted;
        } catch (error) {
          console.error('❌ Error requesting notifications permission:', error);
          return false;
        }
      },
    },
    {
      id: 'activity',
      title: 'Physical Activity',
      description: 'Monitor steps, running, and daily activity patterns',
      icon: 'walk',
      color: '#8B9A7A',
      granted: false,
      requestFunction: async () => {
        try {
          console.log('🔍 Requesting physical activity permissions using NATIVE Android methods...');
          console.log(`📱 Android API Level: ${Platform.Version}`);

          if (Platform.OS === 'android') {
            let hasAnyPermission = false;

            // METHOD 1: Try native PermissionsAndroid (primary method)
            try {
              console.log('🔄 Attempting Method 1: Native PermissionsAndroid for activity...');
              const nativePermissions = [];

              // Add permissions based on Android version
              if (Platform.Version >= 29) {
                nativePermissions.push(PermissionsAndroid.PERMISSIONS.ACTIVITY_RECOGNITION);
              }

              // Always try body sensors for step counting
              nativePermissions.push(PermissionsAndroid.PERMISSIONS.BODY_SENSORS);

              if (nativePermissions.length > 0) {
                const nativeResults = await PermissionsAndroid.requestMultiple(nativePermissions);
                console.log('📱 Method 1 activity results:', nativeResults);

                hasAnyPermission = Object.values(nativeResults).some(
                  result => result === PermissionsAndroid.RESULTS.GRANTED
                );

                if (hasAnyPermission) {
                  console.log('✅ Method 1 activity successful');
                  return true;
                }
              }
            } catch (method1Error) {
              console.log('⚠️ Method 1 activity failed:', method1Error);
            }

            // METHOD 2: Try individual permission requests (maximum compatibility)
            try {
              console.log('🔄 Attempting Method 2: Individual activity permission requests...');

              const individualPermissions = [];

              // Add activity recognition for Android 10+
              if (Platform.Version >= 29) {
                individualPermissions.push({
                  permission: PermissionsAndroid.PERMISSIONS.ACTIVITY_RECOGNITION,
                  title: 'Activity Recognition Permission',
                  message: 'NutriAI needs activity recognition permission for step counting and fitness tracking.'
                });
              }

              // Always try body sensors
              individualPermissions.push({
                permission: PermissionsAndroid.PERMISSIONS.BODY_SENSORS,
                title: 'Body Sensors Permission',
                message: 'NutriAI needs body sensors permission for health monitoring and step tracking.'
              });

              for (const permissionConfig of individualPermissions) {
                try {
                  const result = await PermissionsAndroid.request(
                    permissionConfig.permission,
                    {
                      title: permissionConfig.title,
                      message: permissionConfig.message,
                      buttonNeutral: 'Ask Me Later',
                      buttonNegative: 'Cancel',
                      buttonPositive: 'Allow',
                    }
                  );

                  if (result === PermissionsAndroid.RESULTS.GRANTED) {
                    console.log(`✅ Individual activity permission granted: ${permissionConfig.permission}`);
                    hasAnyPermission = true;
                  }
                } catch (individualError) {
                  console.log(`⚠️ Individual activity permission failed: ${permissionConfig.permission}`, individualError);
                }
              }

              if (hasAnyPermission) {
                console.log('✅ Method 2 activity successful');
                return true;
              }
            } catch (method2Error) {
              console.log('⚠️ Method 2 activity failed:', method2Error);
            }

            // METHOD 3: Try Expo Pedometer as final fallback
            try {
              console.log('🔄 Attempting Method 3: Expo Pedometer for activity...');

              const isPedometerAvailable = await Pedometer.isAvailableAsync();
              console.log('📱 Pedometer available:', isPedometerAvailable);

              if (isPedometerAvailable) {
                const pedometerPermission = await Pedometer.requestPermissionsAsync();
                console.log('📱 Method 3 pedometer result:', pedometerPermission);

                if (pedometerPermission.status === 'granted') {
                  console.log('✅ Method 3 activity successful');
                  hasAnyPermission = true;
                  return true;
                }
              }
            } catch (method3Error) {
              console.log('⚠️ Method 3 activity failed:', method3Error);
            }

            // METHOD 4: Try direct step data access test (compatibility check)
            try {
              console.log('🔄 Attempting Method 4: Direct step data access test...');

              const testSteps = await Pedometer.getStepCountAsync(
                new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
                new Date()
              );
              console.log('📱 Method 4 test step data:', testSteps);

              if (testSteps.steps >= 0) {
                console.log('✅ Method 4 activity successful - step data accessible');
                hasAnyPermission = true;
                return true;
              }
            } catch (method4Error) {
              console.log('⚠️ Method 4 activity failed:', method4Error);
            }

            // If all methods failed, show comprehensive error
            if (!hasAnyPermission) {
              console.log('❌ All activity permission methods failed');

              Alert.alert(
                'Physical Activity Permissions Required',
                `Activity recognition and step counting permissions are required for fitness tracking. Please enable them in Settings.\n\nAndroid ${Platform.Version} detected - tried 4 different permission methods.`,
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Open Settings',
                    onPress: () => Linking.openSettings()
                  }
                ]
              );
              return false;
            }
          }

          console.log('✅ Physical activity permissions granted successfully using native Android methods');
          return true;

        } catch (error) {
          console.error('❌ Error requesting physical activity permissions:', error);
          return false;
        }
      },
    },
  ]);

  const [allGranted, setAllGranted] = useState(false);

  useEffect(() => {
    checkAllPermissions();
  }, []);

  const checkAllPermissions = async () => {
    const updatedPermissions = await Promise.all(
      permissions.map(async (permission) => {
        let granted = false;
        try {
          switch (permission.id) {
            case 'camera':
              const cameraStatus = await ImagePicker.getCameraPermissionsAsync();
              granted = cameraStatus.status === 'granted';
              break;
            case 'microphone':
              const audioStatus = await Audio.getPermissionsAsync();
              granted = audioStatus.status === 'granted';
              break;
            case 'photos':
              const mediaStatus = await ImagePicker.getMediaLibraryPermissionsAsync();
              granted = mediaStatus.status === 'granted';
              break;
            case 'location':
              const locationStatus = await Location.getForegroundPermissionsAsync();
              granted = locationStatus.status === 'granted';
              break;
            case 'sensors':
            case 'activity':
              const isAvailable = await Pedometer.isAvailableAsync();
              if (isAvailable) {
                const pedometerStatus = await Pedometer.getPermissionsAsync();
                granted = pedometerStatus.status === 'granted';
              } else {
                granted = false;
              }
              break;
          }
        } catch (error) {
          console.log(`Error checking ${permission.id} permission:`, error);
        }
        return { ...permission, granted };
      })
    );

    setPermissions(updatedPermissions);
    const allPermissionsGranted = updatedPermissions.every(p => p.granted);
    setAllGranted(allPermissionsGranted);
  };

  const requestPermission = async (permission: Permission) => {
    try {
      console.log(`🔍 User requesting ${permission.title} permission...`);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      const granted = await permission.requestFunction();
      console.log(`📱 ${permission.title} permission result: ${granted ? 'GRANTED' : 'DENIED'}`);

      setPermissions(prev =>
        prev.map(p =>
          p.id === permission.id ? { ...p, granted } : p
        )
      );

      if (!granted) {
        console.log(`⚠️ ${permission.title} permission denied by user`);
        Alert.alert(
          'Permission Required',
          `${permission.title} is needed for the best app experience. You can enable it later in Settings.`,
          [{ text: 'OK' }]
        );
      } else {
        console.log(`✅ ${permission.title} permission granted successfully`);
      }

      // Check if all permissions are now granted
      const updatedPermissions = permissions.map(p =>
        p.id === permission.id ? { ...p, granted } : p
      );
      const allPermissionsGranted = updatedPermissions.every(p => p.granted);
      setAllGranted(allPermissionsGranted);

      console.log(`📊 All permissions status: ${allPermissionsGranted ? 'ALL GRANTED' : 'SOME PENDING'}`);
    } catch (error) {
      console.error(`❌ Error requesting ${permission.title}:`, error);
      Alert.alert('Error', `Failed to request ${permission.title}. Please try again.`);
    }
  };

  const handleContinue = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    onPermissionsGranted();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.header}>
          <Animated.View entering={ZoomIn.delay(400).duration(600)} style={styles.iconContainer}>
            <Ionicons name="shield-checkmark" size={48} color="#6B7C5A" />
          </Animated.View>
          <Text style={styles.title}>App Permissions</Text>
          <Text style={styles.subtitle}>
            Grant permissions to unlock all features and get the best experience
          </Text>
        </Animated.View>

        {/* Permissions List */}
        <View style={styles.permissionsList}>
          {permissions.map((permission, index) => (
            <Animated.View
              key={permission.id}
              entering={SlideInLeft.delay(600 + index * 150).duration(600)}
              style={styles.permissionCard}
            >
              <View style={styles.permissionContent}>
                <View style={[styles.permissionIcon, { backgroundColor: `${permission.color}15` }]}>
                  <Ionicons name={permission.icon} size={24} color={permission.color} />
                </View>

                <View style={styles.permissionText}>
                  <Text style={styles.permissionTitle}>{permission.title}</Text>
                  <Text style={styles.permissionDescription}>{permission.description}</Text>
                </View>

                <TouchableOpacity
                  style={[
                    styles.permissionButton,
                    permission.granted && styles.permissionButtonGranted
                  ]}
                  onPress={() => !permission.granted && requestPermission(permission)}
                  disabled={permission.granted}
                >
                  <Ionicons
                    name={permission.granted ? 'checkmark' : 'add'}
                    size={20}
                    color={permission.granted ? '#FFFFFF' : '#6B7C5A'}
                  />
                </TouchableOpacity>
              </View>
            </Animated.View>
          ))}
        </View>

        {/* Continue Button */}
        <Animated.View entering={FadeInUp.delay(1200).duration(600)} style={styles.continueContainer}>
          <TouchableOpacity
            style={[styles.continueButton, allGranted && styles.continueButtonEnabled]}
            onPress={handleContinue}
          >
            <Text style={[styles.continueText, allGranted && styles.continueTextEnabled]}>
              {allGranted ? 'Continue to App' : 'Continue Anyway'}
            </Text>
            <Ionicons
              name="arrow-forward"
              size={20}
              color={allGranted ? '#FFFFFF' : '#6B7C5A'}
            />
          </TouchableOpacity>

          {!allGranted && (
            <Text style={styles.skipText}>
              You can grant permissions later in Settings
            </Text>
          )}
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: 80,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: '800',
    color: '#6B7C5A',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 24,
  },
  permissionsList: {
    gap: 16,
    marginBottom: 32,
  },
  permissionCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
  },
  permissionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  permissionText: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    lineHeight: 20,
  },
  permissionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#6B7C5A',
  },
  permissionButtonGranted: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  continueContainer: {
    paddingTop: 24,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 32,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderWidth: 2,
    borderColor: '#6B7C5A',
    gap: 12,
  },
  continueButtonEnabled: {
    backgroundColor: '#6B7C5A',
    borderColor: '#6B7C5A',
  },
  continueText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B7C5A',
    letterSpacing: 0.5,
  },
  continueTextEnabled: {
    color: '#FFFFFF',
  },
  skipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    marginTop: 16,
  },
});

export default PermissionsScreen;
