import React, { memo, useMemo, useCallback } from 'react';
import { InteractionManager, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Performance monitoring utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startTimer(operation: string): void {
    this.metrics.set(operation, Date.now());
  }

  endTimer(operation: string): number {
    const startTime = this.metrics.get(operation);
    if (!startTime) return 0;
    
    const duration = Date.now() - startTime;
    console.log(`⚡ Performance: ${operation} took ${duration}ms`);
    this.metrics.delete(operation);
    return duration;
  }

  // Memory usage tracking
  trackMemoryUsage(component: string): void {
    if (__DEV__) {
      console.log(`📊 Memory check for ${component}`);
      // In production, integrate with performance monitoring service
    }
  }
}

// Image caching utility
export class ImageCache {
  private static cache: Map<string, string> = new Map();
  private static readonly CACHE_KEY = 'image_cache';
  private static readonly MAX_CACHE_SIZE = 100;

  static async getCachedImage(url: string): Promise<string | null> {
    // Check memory cache first
    if (this.cache.has(url)) {
      return this.cache.get(url)!;
    }

    // Check persistent cache
    try {
      const cached = await AsyncStorage.getItem(`${this.CACHE_KEY}_${url}`);
      if (cached) {
        this.cache.set(url, cached);
        return cached;
      }
    } catch (error) {
      console.warn('Image cache read error:', error);
    }

    return null;
  }

  static async cacheImage(url: string, base64: string): Promise<void> {
    // Manage cache size
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
      await AsyncStorage.removeItem(`${this.CACHE_KEY}_${firstKey}`);
    }

    // Cache in memory and storage
    this.cache.set(url, base64);
    try {
      await AsyncStorage.setItem(`${this.CACHE_KEY}_${url}`, base64);
    } catch (error) {
      console.warn('Image cache write error:', error);
    }
  }
}

// Component optimization utilities
export const withPerformanceTracking = <P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) => {
  return memo((props: P) => {
    const monitor = PerformanceMonitor.getInstance();
    
    React.useEffect(() => {
      monitor.startTimer(`${componentName}_render`);
      monitor.trackMemoryUsage(componentName);
      
      return () => {
        monitor.endTimer(`${componentName}_render`);
      };
    }, []);

    return <Component {...props} />;
  });
};

// Debounced callback hook for expensive operations
export const useDebouncedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList
): T => {
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    }) as T,
    [callback, delay, ...deps]
  );
};

// Intersection observer for lazy loading
export const useIntersectionObserver = (
  threshold = 0.1
): [React.RefObject<any>, boolean] => {
  const ref = React.useRef<any>(null);
  const [isVisible, setIsVisible] = React.useState(false);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold]);

  return [ref, isVisible];
};

// Batch updates for better performance
export const useBatchedUpdates = () => {
  const batchRef = React.useRef<(() => void)[]>([]);
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  const batchUpdate = useCallback((update: () => void) => {
    batchRef.current.push(update);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      InteractionManager.runAfterInteractions(() => {
        batchRef.current.forEach(update => update());
        batchRef.current = [];
      });
    }, 16); // Next frame
  }, []);

  return batchUpdate;
};

export default PerformanceMonitor;
