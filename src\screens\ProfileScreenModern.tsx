import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Dimensions,
  StatusBar,
  ImageBackground,
  Alert,
  Share,
  TextInput,
  Image,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  FadeInLeft,
  FadeInRight,
  SlideInUp,
  SlideInDown,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
  withSequence,
  withDelay,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows, BlurIntensity } from '../constants/Colors';
import { AnimationConfig } from '../utils/AnimationUtils';
import { ModernCard } from '../components/ModernCard';
import { ModernButton } from '../components/ModernButton';
import { ModernModal } from '../components/ModernModal';
import { ModernLoading } from '../components/ModernLoading';
import { ModernInput } from '../components/ModernInput';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress, NutritionProgress } from '../components/CircularProgress';
import * as Haptics from 'expo-haptics';
import { useProfile } from '../contexts/ProfileContext';

const { width, height } = Dimensions.get('window');

interface StatCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  progress?: number;
  index: number;
}

interface AchievementBadgeProps {
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  earned: boolean;
  progress?: number;
  index: number;
}

interface SettingItemProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  rightElement?: React.ReactNode;
  showChevron?: boolean;
  variant?: 'default' | 'danger' | 'premium';
}

// Modern Stat Card Component
const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  progress = 0,
  index,
}) => {
  const scale = useSharedValue(1);
  const progressWidth = useSharedValue(0);

  useEffect(() => {
    progressWidth.value = withDelay(index * 200, withTiming(progress, { duration: 1000 }));
  }, [progress, index]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const progressAnimatedStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
  };

  return (
    <Animated.View
      entering={ZoomIn.delay(index * 100).duration(600)}
      style={[styles.statCard, animatedStyle]}
    >
      <TouchableOpacity
        style={styles.statCardButton}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <BlurView intensity={10} style={styles.statCardContent}>
          <View style={styles.statCardHeader}>
            <CircularProgress
              size={50}
              progress={progress}
              color={color}
              backgroundColor={Colors.borderLight}
              strokeWidth={3}
              animationDuration={1200}
            >
              <LottieIcon
                name={icon === 'flame' ? 'fire' : icon === 'water' ? 'water' : icon === 'fitness' ? 'heartbeat' : 'progressCircle'}
                size={20}
                color={color}
                enableHaptics={false}
              />
            </CircularProgress>
            <View style={styles.statTextContainer}>
              <Text style={styles.statTitle}>{title}</Text>
              <Text style={[styles.statValue, { color }]}>{value}</Text>
              <Text style={styles.statSubtitle}>{subtitle}</Text>
            </View>
          </View>
        </BlurView>

        {progress > 0 && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <Animated.View style={[styles.progressFill, { backgroundColor: color }, progressAnimatedStyle]} />
            </View>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
};

// Achievement Badge Component
const AchievementBadge: React.FC<AchievementBadgeProps> = ({
  title,
  description,
  icon,
  earned,
  progress = 0,
  index,
}) => {
  const scale = useSharedValue(earned ? 1 : 0.95);
  const opacity = useSharedValue(earned ? 1 : 0.6);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View
      entering={SlideInUp.delay(index * 150).duration(600)}
      style={[styles.achievementBadge, animatedStyle]}
    >
      <View style={[styles.achievementIcon, earned && styles.achievementIconEarned]}>
        <Ionicons
          name={icon}
          size={28}
          color={earned ? Colors.warning : Colors.mutedForeground}
        />
      </View>
      <View style={styles.achievementContent}>
        <Text style={[styles.achievementTitle, earned && styles.achievementTitleEarned]}>
          {title}
        </Text>
        <Text style={styles.achievementDescription}>{description}</Text>
        {!earned && progress > 0 && (
          <View style={styles.achievementProgress}>
            <View style={styles.achievementProgressBar}>
              <View style={[styles.achievementProgressFill, { width: `${progress}%` }]} />
            </View>
            <Text style={styles.achievementProgressText}>{Math.round(progress)}%</Text>
          </View>
        )}
      </View>
    </Animated.View>
  );
};

// Modern Profile Header Component
const ProfileHeader: React.FC<{
  onEditProfile: () => void;
  onShowAchievements: () => void;
  profileImage: string | null;
  onEditProfileImage: () => void;
  profile: any;
}> = ({ onEditProfile, onShowAchievements, profileImage, onEditProfileImage, profile }) => (
    <Animated.View entering={FadeInDown.duration(800)} style={styles.profileHeader}>
      <LinearGradient
        colors={['rgba(107, 124, 90, 0.8)', 'rgba(139, 154, 122, 0.7)', 'rgba(107, 124, 90, 0.6)']}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <TouchableOpacity style={styles.headerButton}>
              <Ionicons name="settings" size={24} color={Colors.brandForeground} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerButton} onPress={() => Share.share({ message: 'Check out this amazing nutrition app!' })}>
              <Ionicons name="share" size={24} color={Colors.brandForeground} />
            </TouchableOpacity>
          </View>

          <Animated.View entering={ZoomIn.delay(300).duration(600)} style={styles.avatarContainer}>
            <View style={styles.avatar}>
              {profileImage ? (
                <Image source={{ uri: profileImage }} style={styles.avatarImage} />
              ) : (
                <LinearGradient
                  colors={[Colors.brandForeground, Colors.background]}
                  style={styles.avatarGradient}
                >
                  <Text style={styles.avatarText}>
                    {profile.name ? profile.name.split(' ').map((n: string) => n[0]).join('') : 'U'}
                  </Text>
                </LinearGradient>
              )}
            </View>
            <View style={styles.statusBadge}>
              <Ionicons name="checkmark" size={14} color={Colors.brandForeground} />
            </View>
            <TouchableOpacity style={styles.editAvatarButton} onPress={onEditProfileImage}>
              <Ionicons name="camera" size={16} color={Colors.brandForeground} />
            </TouchableOpacity>
          </Animated.View>

          <Animated.View entering={SlideInUp.delay(500).duration(600)} style={styles.userInfo}>
            <Text style={styles.userName}>{profile.name || 'User'}</Text>
            <Text style={styles.userEmail}>{profile.email || 'No email set'}</Text>

            <View style={styles.membershipContainer}>
              <LinearGradient
                colors={[Colors.warning, Colors.warning + '80']}
                style={styles.membershipBadge}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Ionicons name="star" size={16} color={Colors.brandForeground} />
                <Text style={styles.membershipText}>Premium Member</Text>
              </LinearGradient>
            </View>

            <View style={styles.profileActions}>
              <ModernButton
                title="Edit Profile"
                onPress={onEditProfile}
                variant="glass"
                size="sm"
                icon="pencil"
                style={styles.editButton}
              />
              <ModernButton
                title="Achievements"
                onPress={onShowAchievements}
                variant="glass"
                size="sm"
                icon="trophy"
                style={styles.achievementsButton}
              />
            </View>
          </Animated.View>
        </View>
      </LinearGradient>
    </Animated.View>
  );

// Stats Card Component
const StatsCard: React.FC = () => (
    <Animated.View entering={FadeInUp.delay(200).duration(800)} style={styles.statsCard}>
      <Text style={styles.statsTitle}>Your Progress</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>28</Text>
          <Text style={styles.statLabel}>Days Active</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>156</Text>
          <Text style={styles.statLabel}>Meals Logged</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>89%</Text>
          <Text style={styles.statLabel}>Goal Achievement</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>12</Text>
          <Text style={styles.statLabel}>Recipes Saved</Text>
        </View>
      </View>
    </Animated.View>
  );

// Settings Section Component
const SettingsSection: React.FC<{ title: string; children: React.ReactNode; delay: number }> = ({
  title,
  children,
  delay
}) => (
    <Animated.View entering={FadeInUp.delay(delay).duration(600)} style={styles.settingsSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.settingsCard}>
        {children}
      </View>
    </Animated.View>
  );

const ProfileScreenModern: React.FC = () => {
  const { profile, dailyData, updateProfile } = useProfile();
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [mealReminders, setMealReminders] = useState(true);
  const [waterReminders, setWaterReminders] = useState(true);
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [showAchievements, setShowAchievements] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);

  // Load profile image on component mount
  useEffect(() => {
    const loadProfileImage = async () => {
      try {
        const savedImage = await AsyncStorage.getItem('profileImage');
        if (savedImage) {
          setProfileImage(savedImage);
        }
      } catch (error) {
        console.error('Error loading profile image:', error);
      }
    };

    loadProfileImage();
  }, []);

  // Function to handle profile picture selection
  const selectProfileImage = async () => {
    try {
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera roll is required!');
        return;
      }

      // Show action sheet
      Alert.alert(
        'Select Profile Picture',
        'Choose an option',
        [
          { text: 'Camera', onPress: () => openCamera() },
          { text: 'Photo Library', onPress: () => openImagePicker() },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const openCamera = async () => {
    try {
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();

      if (cameraPermission.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera is required!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setProfileImage(imageUri);
        await AsyncStorage.setItem('profileImage', imageUri);
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } catch (error) {
      console.error('Error opening camera:', error);
      Alert.alert('Error', 'Failed to open camera');
    }
  };

  const openImagePicker = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setProfileImage(imageUri);
        await AsyncStorage.setItem('profileImage', imageUri);
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } catch (error) {
      console.error('Error opening image picker:', error);
      Alert.alert('Error', 'Failed to open image picker');
    }
  };

  // Dynamic stats based on real user data
  const getStatsData = () => {
    if (!profile.isProfileComplete) {
      return []; // No stats for incomplete profiles
    }

    const stats = [];

    // Current streak (real data)
    if (profile.currentStreak > 0) {
      stats.push({
        title: 'Streak',
        value: profile.currentStreak.toString(),
        subtitle: 'days in a row',
        icon: 'flame' as const,
        color: Colors.warning,
        progress: Math.min((profile.currentStreak / 30) * 100, 100), // Progress towards 30-day goal
      });
    }

    // Current weight (real data)
    if (profile.weight > 0) {
      stats.push({
        title: 'Weight',
        value: profile.weight.toString(),
        subtitle: 'kg current',
        icon: 'fitness' as const,
        color: Colors.success,
        progress: Math.max(0, Math.min(100, 70)), // Simplified progress calculation
      });
    }

    // Today's calories (real data)
    if (dailyData.caloriesConsumed > 0 || profile.caloriesGoal > 0) {
      const calorieProgress = (dailyData.caloriesConsumed / profile.caloriesGoal) * 100;
      stats.push({
        title: 'Calories',
        value: dailyData.caloriesConsumed.toString(),
        subtitle: `of ${profile.caloriesGoal} goal`,
        icon: 'nutrition' as const,
        color: Colors.brand,
        progress: Math.min(calorieProgress, 100),
      });
    }

    // Water intake (real data)
    if (dailyData.waterConsumed > 0 || profile.waterGoal > 0) {
      const waterProgress = (dailyData.waterConsumed / profile.waterGoal) * 100;
      stats.push({
        title: 'Water',
        value: dailyData.waterConsumed.toString(),
        subtitle: `of ${profile.waterGoal} glasses`,
        icon: 'water' as const,
        color: Colors.info,
        progress: Math.min(waterProgress, 100),
      });
    }

    return stats;
  };

  const statsData = getStatsData();

  // Dynamic achievements based on real user progress
  const getAchievements = () => {
    const achievements = [];

    // First Scan Achievement
    if (profile.totalMealsLogged > 0) {
      achievements.push({
        id: '1',
        title: 'First Scan',
        description: 'Scanned your first food item',
        icon: 'camera' as const,
        earned: true,
        progress: 100,
      });
    } else {
      achievements.push({
        id: '1',
        title: 'First Scan',
        description: 'Scan your first food item',
        icon: 'camera' as const,
        earned: false,
        progress: 0,
      });
    }

    // Week Warrior Achievement
    if (profile.currentStreak >= 7) {
      achievements.push({
        id: '2',
        title: 'Week Warrior',
        description: 'Tracked meals for 7 days straight',
        icon: 'calendar' as const,
        earned: true,
        progress: 100,
      });
    } else {
      achievements.push({
        id: '2',
        title: 'Week Warrior',
        description: 'Track meals for 7 days straight',
        icon: 'calendar' as const,
        earned: false,
        progress: Math.round((profile.currentStreak / 7) * 100),
      });
    }

    // Nutrition Expert Achievement (based on profile completeness and usage)
    const nutritionProgress = profile.isProfileComplete ? 50 : 0;
    achievements.push({
      id: '3',
      title: 'Nutrition Expert',
      description: 'Complete your nutrition profile',
      icon: 'school' as const,
      earned: profile.isProfileComplete,
      progress: nutritionProgress,
    });

    // Recipe Master Achievement (based on meals logged)
    const recipeProgress = Math.min((profile.totalMealsLogged / 25) * 100, 100);
    achievements.push({
      id: '4',
      title: 'Recipe Master',
      description: 'Log 25 meals',
      icon: 'book' as const,
      earned: profile.totalMealsLogged >= 25,
      progress: Math.round(recipeProgress),
    });

    // Goal Crusher Achievement (based on daily progress)
    const goalProgress = Math.round(profile.dailyProgress);
    achievements.push({
      id: '5',
      title: 'Goal Crusher',
      description: 'Achieve your daily nutrition goals',
      icon: 'trophy' as const,
      earned: profile.dailyProgress >= 100,
      progress: goalProgress,
    });

    return achievements;
  };

  const achievements = getAchievements();

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Beautiful Background with Image */}
      <ImageBackground
        source={{
          uri: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'
        }}
        style={styles.backgroundContainer}
        resizeMode="cover"
        onError={(error) => console.log('Background image failed to load:', error)}
        onLoad={() => console.log('Background image loaded successfully')}
      >
        <View style={styles.whiteOverlay} />
      </ImageBackground>

      {/* Billion-Dollar Profile Header */}
      <ProfileHeader
        onEditProfile={() => setShowEditProfile(true)}
        onShowAchievements={() => setShowAchievements(true)}
        profileImage={profileImage}
        onEditProfileImage={selectProfileImage}
        profile={profile}
      />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Stats Grid */}
        <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Your Progress</Text>
          {statsData.length > 0 ? (
            <View style={styles.statsGrid}>
              {statsData.map((stat, index) => (
                <StatCard
                  key={stat.title}
                  title={stat.title}
                  value={stat.value}
                  subtitle={stat.subtitle}
                  icon={stat.icon}
                  color={stat.color}
                  progress={stat.progress}
                  index={index}
                />
              ))}
            </View>
          ) : (
            <View style={styles.emptyStatsState}>
              <Ionicons name="analytics-outline" size={48} color="#E5E7EB" />
              <Text style={styles.emptyStatsText}>Complete your profile to see progress</Text>
              <Text style={styles.emptyStatsSubtext}>Set your goals and start tracking</Text>
            </View>
          )}
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View entering={FadeInUp.delay(300).duration(600)} style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <ModernButton
              title="View Reports"
              onPress={() => {}}
              variant="primary"
              size="md"
              icon="analytics"
              style={styles.quickActionButton}
            />
            <ModernButton
              title="Export Data"
              onPress={() => {}}
              variant="secondary"
              size="md"
              icon="download"
              style={styles.quickActionButton}
            />
          </View>
        </Animated.View>

        {/* Settings Sections */}
        <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.settingsContainer}>
          <Text style={styles.sectionTitle}>Settings</Text>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="flag"
              title="Daily Calorie Goal"
              subtitle="2,000 calories"
              onPress={() => {}}
            />
            <SettingItem
              icon="water"
              title="Water Intake Goal"
              subtitle="8 glasses per day"
              onPress={() => {}}
            />
            <SettingItem
              icon="fitness"
              title="Activity Level"
              subtitle="Moderately Active"
              onPress={() => {}}
            />
            <SettingItem
              icon="fitness"
              title="Weight Goal"
              subtitle="Maintain current weight"
              onPress={() => {}}
              showChevron={false}
            />
          </ModernCard>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="restaurant"
              title="Dietary Restrictions"
              subtitle="Vegetarian, Gluten-free"
              onPress={() => {}}
            />
            <SettingItem
              icon="leaf"
              title="Food Allergies"
              subtitle="Nuts, Shellfish"
              onPress={() => {}}
            />
            <SettingItem
              icon="time"
              title="Meal Times"
              subtitle="Breakfast 8AM, Lunch 1PM, Dinner 7PM"
              onPress={() => {}}
            />
          </ModernCard>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="notifications"
              title="Push Notifications"
              subtitle="Get reminders and updates"
              rightElement={
                <Switch
                  value={notifications}
                  onValueChange={setNotifications}
                  trackColor={{ false: Colors.muted, true: Colors.brandMuted }}
                  thumbColor={notifications ? Colors.brand : Colors.mutedForeground}
                />
              }
              showChevron={false}
            />
            <SettingItem
              icon="restaurant"
              title="Meal Reminders"
              subtitle="Remind me to log meals"
              rightElement={
                <Switch
                  value={mealReminders}
                  onValueChange={setMealReminders}
                  trackColor={{ false: Colors.muted, true: Colors.brandMuted }}
                  thumbColor={mealReminders ? Colors.brand : Colors.mutedForeground}
                />
              }
              showChevron={false}
            />
            <SettingItem
              icon="water"
              title="Water Reminders"
              subtitle="Stay hydrated throughout the day"
              rightElement={
                <Switch
                  value={waterReminders}
                  onValueChange={setWaterReminders}
                  trackColor={{ false: Colors.muted, true: Colors.brandMuted }}
                  thumbColor={waterReminders ? Colors.brand : Colors.mutedForeground}
                />
              }
              showChevron={false}
            />
          </ModernCard>

          <ModernCard variant="default" title="" style={styles.settingsCard}>
            <SettingItem
              icon="help-circle"
              title="Help & Support"
              subtitle="Get help and contact support"
              onPress={() => {}}
            />
            <SettingItem
              icon="shield-checkmark"
              title="Privacy Policy"
              subtitle="Read our privacy policy"
              onPress={() => {}}
            />
            <SettingItem
              icon="document-text"
              title="Terms of Service"
              subtitle="View terms and conditions"
              onPress={() => {}}
            />
            <SettingItem
              icon="log-out"
              title="Sign Out"
              subtitle="Sign out of your account"
              onPress={() => Alert.alert('Sign Out', 'Are you sure you want to sign out?')}
              variant="danger"
            />
          </ModernCard>
        </Animated.View>

        {/* App Version */}
        <Animated.View entering={FadeInUp.delay(1600).duration(600)} style={styles.versionSection}>
          <Text style={styles.versionText}>Nutri AI v1.0.0</Text>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Edit Profile Modal */}
      <ModernModal
        visible={showEditProfile}
        onClose={() => setShowEditProfile(false)}
        title="Edit Profile"
        variant="center"
        size="lg"
      >
        <View style={styles.editProfileContent}>
          <Text style={styles.modalLabel}>Name</Text>
          <ModernInput
            value={profile.name || ''}
            onChangeText={() => {}}
            placeholder="Enter your name"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Email</Text>
          <ModernInput
            value={profile.email || ''}
            onChangeText={(text) => {
              // Update profile email when changed
              updateProfile('email', text);
            }}
            placeholder="Enter your email"
            keyboardType="email-address"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Height (cm)</Text>
          <ModernInput
            value="165"
            onChangeText={() => {}}
            placeholder="Enter your height"
            keyboardType="numeric"
            variant="filled"
            style={styles.modalInput}
          />

          <Text style={styles.modalLabel}>Weight (kg)</Text>
          <ModernInput
            value="68.5"
            onChangeText={() => {}}
            placeholder="Enter your weight"
            keyboardType="numeric"
            variant="filled"
            style={styles.modalInput}
          />

          <View style={styles.modalButtons}>
            <ModernButton
              title="Cancel"
              onPress={() => setShowEditProfile(false)}
              variant="outline"
              size="md"
              style={styles.modalCancelButton}
            />
            <ModernButton
              title="Save Changes"
              onPress={() => setShowEditProfile(false)}
              variant="primary"
              size="md"
              icon="checkmark"
              style={styles.modalSaveButton}
            />
          </View>
        </View>
      </ModernModal>

      {/* Achievements Modal */}
      <ModernModal
        visible={showAchievements}
        onClose={() => setShowAchievements(false)}
        title="Achievements"
        variant="fullscreen"
      >
        <ScrollView style={styles.achievementsScroll} showsVerticalScrollIndicator={false}>
          <View style={styles.achievementsGrid}>
            {achievements.map((achievement, index) => (
              <AchievementBadge
                key={achievement.title}
                title={achievement.title}
                description={achievement.description}
                icon={achievement.icon}
                earned={achievement.earned}
                progress={achievement.progress}
                index={index}
              />
            ))}
          </View>
        </ScrollView>
      </ModernModal>
    </View>
  );
};

// Setting Item Component
const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  rightElement,
  showChevron = true,
  variant = 'default',
}) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    if (onPress) {
      scale.value = withSpring(0.98, { damping: 15, stiffness: 400 });
    }
  };

  const handlePressOut = () => {
    if (onPress) {
      scale.value = withSpring(1, { damping: 15, stiffness: 400 });
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'danger': return Colors.error;
      case 'premium': return Colors.warning;
      default: return Colors.brand;
    }
  };

  const getTitleColor = () => {
    switch (variant) {
      case 'danger': return Colors.error;
      default: return Colors.foreground;
    }
  };

  return (
    <Animated.View style={[styles.settingItem, animatedStyle]}>
      <TouchableOpacity
        style={styles.settingItemButton}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={!onPress}
        activeOpacity={1}
      >
        <View style={styles.settingItemLeft}>
          <View style={[styles.settingIcon, { backgroundColor: getIconColor() + '20' }]}>
            <Ionicons name={icon} size={20} color={getIconColor()} />
          </View>
          <View style={styles.settingContent}>
            <Text style={[styles.settingTitle, { color: getTitleColor() }]}>{title}</Text>
            {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
          </View>
        </View>

        <View style={styles.settingItemRight}>
          {rightElement}
          {showChevron && onPress && (
            <Ionicons name="chevron-forward" size={20} color={Colors.mutedForeground} />
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Make transparent to show background image
  },

  // Beautiful Background
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.85)', // Reduced opacity to make background more visible
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 140, // Increased by 50px from 90px
  },

  // Profile Header
  profileHeader: {
    marginBottom: Spacing.xl,
  },
  headerGradient: {
    paddingTop: 80, // Consistent with other screens
    paddingBottom: Spacing.xxxl,
    paddingHorizontal: Spacing.xl,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: Spacing.xl,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: Spacing.xl,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    ...Shadows.xl,
  },
  avatarImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.brandForeground,
  },
  statusBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.success,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  userName: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
  },
  userInfo: {
    alignItems: 'center',
  },
  membershipContainer: {
    marginBottom: Spacing.lg,
  },
  membershipBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.brandMuted,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    gap: Spacing.xs,
  },
  membershipText: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.brand,
  },
  profileActions: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  editButton: {
    flex: 1,
  },
  achievementsButton: {
    flex: 1,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },

  // Stats Section
  statsSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xl,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
  },

  // Stat Card
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    ...Shadows.sm,
  },
  statCardButton: {
    padding: Spacing.lg,
  },
  statCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  statTitle: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    color: Colors.mutedForeground,
    flex: 1,
  },
  statValue: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    marginBottom: Spacing.xs,
  },
  statSubtitle: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
  },
  progressContainer: {
    marginTop: Spacing.sm,
  },
  progressBar: {
    height: 4,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: BorderRadius.sm,
  },

  // Achievement Badge
  achievementBadge: {
    flexDirection: 'row',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    ...Shadows.sm,
  },
  achievementIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.lg,
  },
  achievementIconEarned: {
    backgroundColor: Colors.warning + '20',
  },
  achievementContent: {
    flex: 1,
    justifyContent: 'center',
  },
  achievementTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  achievementTitleEarned: {
    color: Colors.foreground,
  },
  achievementDescription: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    lineHeight: 18,
  },
  achievementProgress: {
    marginTop: Spacing.md,
  },
  achievementProgressBar: {
    height: 4,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
    overflow: 'hidden',
    marginBottom: Spacing.xs,
  },
  achievementProgressFill: {
    height: '100%',
    backgroundColor: Colors.brand,
    borderRadius: BorderRadius.sm,
  },
  achievementProgressText: {
    fontSize: FontSizes.xs,
    color: Colors.mutedForeground,
    textAlign: 'right',
  },


  // Legacy Stats Card (for StatsCard component)
  statsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.xl,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  statsTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: Spacing.md,
  },
  statLabel: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },

  // Quick Actions
  quickActionsSection: {
    paddingHorizontal: Spacing.xl,
    marginBottom: Spacing.xl,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  quickActionButton: {
    flex: 1,
  },

  // Settings Container
  settingsContainer: {
    paddingHorizontal: Spacing.xl,
  },

  // Settings Sections
  settingsSection: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  settingsCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    overflow: 'hidden',
  },
  settingItem: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  settingItemButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
    width: '100%',
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },
  settingSubtitle: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },

  // Sign Out Section
  signOutSection: {
    marginBottom: Spacing.xl,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.error,
    borderRadius: BorderRadius.lg,
    paddingVertical: Spacing.lg,
    gap: Spacing.sm,
  },
  signOutText: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.error,
  },

  // Version Section
  versionSection: {
    alignItems: 'center',
    paddingBottom: Spacing.xl,
  },
  versionText: {
    fontSize: FontSizes.sm,
    color: Colors.mutedForeground,
  },

  // Modal Styles
  editProfileContent: {
    gap: Spacing.lg,
  },
  modalLabel: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  modalInput: {
    marginBottom: Spacing.md,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  modalCancelButton: {
    flex: 1,
  },
  modalSaveButton: {
    flex: 1,
  },
  achievementsScroll: {
    flex: 1,
  },
  achievementsGrid: {
    padding: Spacing.lg,
  },

  // Premium Stat Card Styles
  statCardContent: {
    borderRadius: 16,
    overflow: 'hidden',
    padding: 16,
  },
  statTextContainer: {
    flex: 1,
    marginLeft: 12,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },

  // Empty states
  emptyStatsState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStatsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 12,
  },
  emptyStatsSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 4,
  },
});

export default ProfileScreenModern;
