import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  ZoomIn,
  BounceIn,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { useOnboarding } from '../../contexts/OnboardingContext';
import { useAppState } from '../../contexts/AppStateContext';
import LottieIcon from '../../components/LottieIcon';
import NutritionBackground from '../../components/NutritionBackground';

const CompletionScreen: React.FC = () => {
  const { data, completeOnboarding } = useOnboarding();
  const { completeOnboardingFlow } = useAppState();

  useEffect(() => {
    // Celebration haptics
    setTimeout(() => {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }, 500);
  }, []);

  const handleGetStarted = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

    try {
      // Complete onboarding - this will set AsyncStorage
      await completeOnboarding();

      // Immediately update app state to switch to main app
      completeOnboardingFlow();

      console.log('Onboarding completed successfully');

    } catch (error) {
      console.error('Error completing onboarding:', error);
      // Still try to complete the flow
      completeOnboardingFlow();
    }
  };

  const getPersonalizedMessage = () => {
    const firstName = data.name.split(' ')[0];
    const goals = data.fitnessObjectives.length;
    const preferences = data.dietaryPreferences.length;
    
    return `Welcome ${firstName}! We've personalized your experience with ${goals} fitness goal${goals !== 1 ? 's' : ''} and ${preferences} dietary preference${preferences !== 1 ? 's' : ''}.`;
  };

  return (
    <NutritionBackground variant="onboarding">
      <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent}>
        {/* Success Animation */}
        <Animated.View entering={ZoomIn.delay(200).duration(800)} style={styles.iconContainer}>
          <Image
            source={require('../../../assets/image final.png')}
            style={styles.logoImage}
            resizeMode="contain"
          />
        </Animated.View>

        {/* Title */}
        <Animated.Text entering={FadeInUp.delay(600).duration(600)} style={styles.title}>
          You're All Set!
        </Animated.Text>

      {/* Personalized Message */}
      <Animated.Text entering={FadeInUp.delay(800).duration(600)} style={styles.message}>
        {getPersonalizedMessage()}
      </Animated.Text>

      {/* Features Preview */}
      <Animated.View entering={FadeInUp.delay(1000).duration(600)} style={styles.featuresContainer}>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={24} color="#6B7C5A" />
          <Text style={styles.featureText}>Personalized meal recommendations</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={24} color="#6B7C5A" />
          <Text style={styles.featureText}>AI-powered nutrition tracking</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={24} color="#6B7C5A" />
          <Text style={styles.featureText}>Custom calorie and macro goals</Text>
        </View>
        <View style={styles.featureItem}>
          <Ionicons name="checkmark-circle" size={24} color="#6B7C5A" />
          <Text style={styles.featureText}>Smart food scanning technology</Text>
        </View>
      </Animated.View>

      {/* Get Started Button */}
      <Animated.View entering={BounceIn.delay(1400).duration(800)} style={styles.buttonContainer}>
        <TouchableOpacity style={styles.getStartedButton} onPress={handleGetStarted}>
          <View style={styles.buttonContent}>
            <Text style={styles.buttonText}>Start Your Journey</Text>
            <Ionicons name="arrow-forward" size={24} color="white" />
          </View>
        </TouchableOpacity>
      </Animated.View>

      {/* Footer */}
      <Animated.Text entering={FadeInDown.delay(1600).duration(600)} style={styles.footer}>
        Your data is securely stored and ready to personalize your experience
      </Animated.Text>
    </ScrollView>
    </NutritionBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 80,
    paddingBottom: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 32,
    backgroundColor: '#F9FAFB',
    borderRadius: 80,
    padding: 20,
    borderWidth: 3,
    borderColor: '#6B7C5A',
  },
  logoImage: {
    width: 160,
    height: 64,
  },
  title: {
    fontSize: 36,
    fontWeight: '900',
    color: '#6B7C5A',
    textAlign: 'center',
    marginBottom: 24,
    letterSpacing: -0.5,
  },
  message: {
    fontSize: 18,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: 40,
    paddingHorizontal: 16,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: 48,
    backgroundColor: '#F9FAFB',
    borderRadius: 24,
    padding: 24,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  featureText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 12,
    letterSpacing: 0.3,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 24,
  },
  getStartedButton: {
    borderRadius: 32,
    overflow: 'hidden',
    backgroundColor: '#6B7C5A',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 48,
    gap: 12,
  },
  buttonText: {
    fontSize: 20,
    fontWeight: '800',
    color: '#FFFFFF',
    letterSpacing: 0.5,
  },
  footer: {
    fontSize: 14,
    fontWeight: '500',
    color: '#8B9A7A',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CompletionScreen;
