{"name": "nutri-ai", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/config": "~8.1.1", "@expo/config-plugins": "~7.2.2", "@expo/prebuild-config": "~6.2.4", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/cli-server-api": "^18.0.0", "@react-native-voice/voice": "^3.2.4", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "expo": "~49.0.0", "expo-av": "~13.4.1", "expo-blur": "~12.4.1", "expo-clipboard": "~4.3.1", "expo-dev-client": "~2.4.13", "expo-haptics": "~12.4.0", "expo-image-picker": "~14.3.2", "expo-linear-gradient": "~12.3.0", "expo-location": "~16.1.0", "expo-notifications": "^0.20.1", "expo-sensors": "~12.3.0", "expo-sharing": "~11.5.0", "expo-status-bar": "~1.6.0", "expo-updates": "~0.18.19", "fbjs": "^3.0.5", "invariant": "^2.2.4", "lottie-react-native": "5.1.6", "metro-react-native-babel-transformer": "^0.77.0", "react": "18.2.0", "react-native": "0.72.10", "react-native-gesture-handler": "~2.12.0", "react-native-haptic-feedback": "^2.3.3", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "expo-splash-screen": "~0.20.5"}, "devDependencies": {"@babel/core": "^7.28.0", "@types/react": "~18.2.14", "typescript": "~5.8.3"}, "private": true}