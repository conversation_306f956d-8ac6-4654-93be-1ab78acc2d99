import DatabaseService from './DatabaseService';
import { MealEntry } from '../contexts/ProfileContext';

// Integration service to bridge existing app logic with the new database
class DatabaseIntegrationService {
  private static instance: DatabaseIntegrationService;
  private db = DatabaseService;

  public static getInstance(): DatabaseIntegrationService {
    if (!DatabaseIntegrationService.instance) {
      DatabaseIntegrationService.instance = new DatabaseIntegrationService();
    }
    return DatabaseIntegrationService.instance;
  }

  // Profile Integration
  async syncProfileToDatabase(profile: any): Promise<void> {
    try {
      await this.db.saveUserProfile(profile);
      console.log('✅ Profile synced to database');
    } catch (error) {
      console.error('❌ Error syncing profile to database:', error);
    }
  }

  async loadProfileFromDatabase(): Promise<any | null> {
    try {
      const profile = await this.db.getUserProfile();
      if (profile) {
        console.log('✅ Profile loaded from database');
        return profile;
      }
      return null;
    } catch (error) {
      console.error('❌ Error loading profile from database:', error);
      return null;
    }
  }

  // Meal Logs Integration
  async syncMealToDatabase(meal: MealEntry): Promise<string | null> {
    try {
      const mealId = await this.db.saveMealLog({
        name: meal.name,
        calories: meal.calories,
        protein: meal.protein,
        carbs: meal.carbs,
        fat: meal.fat,
        type: meal.type,
        timestamp: meal.timestamp
      });
      console.log('✅ Meal synced to database:', mealId);
      return mealId;
    } catch (error) {
      console.error('❌ Error syncing meal to database:', error);
      return null;
    }
  }

  async loadMealsFromDatabase(date?: string): Promise<MealEntry[]> {
    try {
      const dbMeals = await this.db.getMealLogs(date);
      const meals: MealEntry[] = dbMeals.map(dbMeal => ({
        id: dbMeal.id,
        name: dbMeal.name,
        time: new Date(dbMeal.timestamp).toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit' 
        }),
        calories: dbMeal.calories,
        protein: dbMeal.protein,
        carbs: dbMeal.carbs,
        fat: dbMeal.fat,
        type: dbMeal.type,
        timestamp: dbMeal.timestamp
      }));
      
      console.log(`✅ Loaded ${meals.length} meals from database`);
      return meals;
    } catch (error) {
      console.error('❌ Error loading meals from database:', error);
      return [];
    }
  }

  async getRecentMealsFromDatabase(limit: number = 10): Promise<MealEntry[]> {
    try {
      const dbMeals = await this.db.getRecentMeals(limit);
      const meals: MealEntry[] = dbMeals.map(dbMeal => ({
        id: dbMeal.id,
        name: dbMeal.name,
        time: new Date(dbMeal.timestamp).toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit' 
        }),
        calories: dbMeal.calories,
        protein: dbMeal.protein,
        carbs: dbMeal.carbs,
        fat: dbMeal.fat,
        type: dbMeal.type,
        timestamp: dbMeal.timestamp
      }));
      
      return meals;
    } catch (error) {
      console.error('❌ Error loading recent meals from database:', error);
      return [];
    }
  }

  // Health Data Integration
  async syncHealthDataToDatabase(healthData: {
    steps: number;
    heartRate?: number;
    weight?: number;
    waterIntake: number;
    sleepHours?: number;
    energyLevel?: number;
    mood?: string;
  }): Promise<string | null> {
    try {
      const healthId = await this.db.saveHealthData(healthData);
      console.log('✅ Health data synced to database:', healthId);
      return healthId;
    } catch (error) {
      console.error('❌ Error syncing health data to database:', error);
      return null;
    }
  }

  async loadTodaysHealthData(): Promise<any | null> {
    try {
      const healthData = await this.db.getTodaysHealthData();
      if (healthData) {
        console.log('✅ Today\'s health data loaded from database');
        return {
          steps: healthData.steps,
          heartRate: healthData.heartRate,
          weight: healthData.weight,
          waterIntake: healthData.waterIntake,
          sleepHours: healthData.sleepHours,
          energyLevel: healthData.energyLevel,
          mood: healthData.mood
        };
      }
      return null;
    } catch (error) {
      console.error('❌ Error loading health data from database:', error);
      return null;
    }
  }

  // Weekly Plans Integration
  async syncWeeklyPlanToDatabase(plan: any, customizations: any = {}): Promise<string | null> {
    try {
      const planId = await this.db.saveWeeklyPlan(plan, customizations);
      console.log('✅ Weekly plan synced to database:', planId);
      return planId;
    } catch (error) {
      console.error('❌ Error syncing weekly plan to database:', error);
      return null;
    }
  }

  async loadActiveWeeklyPlan(): Promise<any | null> {
    try {
      const planData = await this.db.getActiveWeeklyPlan();
      if (planData) {
        console.log('✅ Active weekly plan loaded from database');
        return planData.plan;
      }
      return null;
    } catch (error) {
      console.error('❌ Error loading weekly plan from database:', error);
      return null;
    }
  }

  // Scan History Integration
  async syncScanToDatabase(imageUri: string, analysis: any, mealLogged: boolean = false): Promise<string | null> {
    try {
      const scanId = await this.db.saveScanHistory({
        imageUri,
        analysis,
        timestamp: Date.now(),
        mealLogged
      });
      console.log('✅ Scan synced to database:', scanId);
      return scanId;
    } catch (error) {
      console.error('❌ Error syncing scan to database:', error);
      return null;
    }
  }

  async loadScanHistory(limit?: number): Promise<any[]> {
    try {
      const scans = await this.db.getScanHistory(limit);
      console.log(`✅ Loaded ${scans.length} scans from database`);
      return scans;
    } catch (error) {
      console.error('❌ Error loading scan history from database:', error);
      return [];
    }
  }

  // Recipe Integration
  async syncRecipeToDatabase(recipe: {
    title: string;
    ingredients: string[];
    instructions: string[];
    nutrition: any;
    tags: string[];
    imageUrl?: string;
    cookTime?: string;
    difficulty?: string;
    servings?: number;
    isFavorite?: boolean;
  }): Promise<string | null> {
    try {
      const recipeId = await this.db.saveRecipe({
        ...recipe,
        isFavorite: recipe.isFavorite || false
      });
      console.log('✅ Recipe synced to database:', recipeId);
      return recipeId;
    } catch (error) {
      console.error('❌ Error syncing recipe to database:', error);
      return null;
    }
  }

  async loadRecipesFromDatabase(): Promise<any[]> {
    try {
      const recipes = await this.db.getRecipes();
      console.log(`✅ Loaded ${recipes.length} recipes from database`);
      return recipes;
    } catch (error) {
      console.error('❌ Error loading recipes from database:', error);
      return [];
    }
  }

  async loadFavoriteRecipes(): Promise<any[]> {
    try {
      const favorites = await this.db.getFavoriteRecipes();
      console.log(`✅ Loaded ${favorites.length} favorite recipes from database`);
      return favorites;
    } catch (error) {
      console.error('❌ Error loading favorite recipes from database:', error);
      return [];
    }
  }

  // Preferences Integration
  async syncPreferencesToDatabase(preferences: {
    notificationSettings: any;
    dietaryPreferences: string[];
    mealTimes: any;
    units: 'metric' | 'imperial';
    theme: 'light' | 'dark' | 'auto';
  }): Promise<void> {
    try {
      await this.db.savePreferences(preferences);
      console.log('✅ Preferences synced to database');
    } catch (error) {
      console.error('❌ Error syncing preferences to database:', error);
    }
  }

  async loadPreferencesFromDatabase(): Promise<any | null> {
    try {
      const preferences = await this.db.getPreferences();
      if (preferences) {
        console.log('✅ Preferences loaded from database');
        return {
          notificationSettings: preferences.notificationSettings,
          dietaryPreferences: preferences.dietaryPreferences,
          mealTimes: preferences.mealTimes,
          units: preferences.units,
          theme: preferences.theme
        };
      }
      return null;
    } catch (error) {
      console.error('❌ Error loading preferences from database:', error);
      return null;
    }
  }

  // Nutrition Calculation from Database
  async calculateTodaysNutrition(): Promise<{
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    mealCount: number;
  }> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const todaysMeals = await this.loadMealsFromDatabase(today);
      
      const nutrition = todaysMeals.reduce((total, meal) => ({
        calories: total.calories + (meal.calories || 0),
        protein: total.protein + (meal.protein || 0),
        carbs: total.carbs + (meal.carbs || 0),
        fat: total.fat + (meal.fat || 0),
        mealCount: total.mealCount + 1
      }), { calories: 0, protein: 0, carbs: 0, fat: 0, mealCount: 0 });

      console.log('✅ Calculated today\'s nutrition from database:', nutrition);
      return nutrition;
    } catch (error) {
      console.error('❌ Error calculating nutrition from database:', error);
      return { calories: 0, protein: 0, carbs: 0, fat: 0, mealCount: 0 };
    }
  }

  // Database Maintenance
  async performDatabaseMaintenance(): Promise<void> {
    try {
      // Clean old data (keep last 30 days)
      await this.db.clearOldData(30);
      
      // Log database size
      const sizes = await this.db.getDatabaseSize();
      console.log('📊 Database sizes:', sizes);
      
      console.log('✅ Database maintenance completed');
    } catch (error) {
      console.error('❌ Error during database maintenance:', error);
    }
  }

  // Data Migration and Backup
  async exportUserData(): Promise<any> {
    try {
      const exportData = await this.db.exportAllData();
      console.log('✅ User data exported successfully');
      return exportData;
    } catch (error) {
      console.error('❌ Error exporting user data:', error);
      return null;
    }
  }

  async clearAllUserData(): Promise<void> {
    try {
      await this.db.clearAllData();
      console.log('✅ All user data cleared from database');
    } catch (error) {
      console.error('❌ Error clearing user data:', error);
      throw error;
    }
  }
}

export default DatabaseIntegrationService.getInstance();
