import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width: screenWidth } = Dimensions.get('window');

interface TabBarProps {
  state: any;
  descriptors: any;
  navigation: any;
}

interface TabConfig {
  name: string;
  icon: keyof typeof Ionicons.glyphMap;
  iconFocused: keyof typeof Ionicons.glyphMap;
  color: string;
}

const tabConfigs: Record<string, TabConfig> = {
  Home: {
    name: 'Home',
    icon: 'home-outline',
    iconFocused: 'home',
    color: '#6B7C5A',
  },
  Recipes: {
    name: 'Recipes',
    icon: 'restaurant-outline',
    iconFocused: 'restaurant',
    color: '#8B9A7A',
  },
  Scanner: {
    name: 'Scanner',
    icon: 'camera-outline',
    iconFocused: 'camera',
    color: '#5A6B4A',
  },
  Plan: {
    name: 'Plan',
    icon: 'calendar-outline',
    iconFocused: 'calendar',
    color: '#7A8B6A',
  },
  Profile: {
    name: 'Profile',
    icon: 'person-outline',
    iconFocused: 'person',
    color: '#6B7C5A',
  },
};

const AnimatedTabBar: React.FC<TabBarProps> = ({ state, descriptors, navigation }) => {
  const insets = useSafeAreaInsets();
  const tabWidth = screenWidth / state.routes.length;
  
  // Animated values for each tab
  const animatedValues = state.routes.map(() => ({
    scale: useSharedValue(1),
    opacity: useSharedValue(0.6),
  }));

  // Line indicator animation
  const indicatorPosition = useSharedValue(state.index * tabWidth);

  React.useEffect(() => {
    // Animate indicator position
    indicatorPosition.value = withSpring(state.index * tabWidth, {
      damping: 20,
      stiffness: 200,
    });

    // Animate tabs
    animatedValues.forEach((values, index) => {
      const isActive = index === state.index;

      values.scale.value = withSpring(isActive ? 1.1 : 1, {
        damping: 15,
        stiffness: 300,
      });

      values.opacity.value = withTiming(isActive ? 1 : 0.6, {
        duration: 200,
      });
    });
  }, [state.index]);

  const handleTabPress = (index: number, routeName: string) => {
    // Haptic feedback
    runOnJS(() => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    })();

    // Navigate to tab
    const event = navigation.emit({
      type: 'tabPress',
      target: state.routes[index].key,
      canPreventDefault: true,
    });

    if (!event.defaultPrevented) {
      navigation.navigate(routeName);
    }
  };

  // Animated indicator style
  const indicatorStyle = useAnimatedStyle(() => ({
    transform: [
      {
        translateX: indicatorPosition.value,
      },
    ],
  }));

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      {/* Solid Background Container */}
      <View style={styles.solidContainer}>
        {/* Line Indicator */}
        <Animated.View style={[styles.lineIndicator, indicatorStyle, { width: tabWidth }]} />

        {/* Tab Buttons */}
        <View style={styles.tabContainer}>
          {state.routes.map((route: any, index: number) => {
            const config = tabConfigs[route.name];
            const isActive = index === state.index;

            if (!config) return null;

            const animatedStyle = useAnimatedStyle(() => ({
              transform: [{ scale: animatedValues[index].scale.value }],
              opacity: animatedValues[index].opacity.value,
            }));

            return (
              <TouchableOpacity
                key={route.key}
                style={styles.tabButton}
                onPress={() => handleTabPress(index, route.name)}
                activeOpacity={0.8}
              >
                <Animated.View style={[styles.tabContent, animatedStyle]}>
                  {/* Special treatment for Scanner tab */}
                  {route.name === 'Scanner' ? (
                    <View style={[styles.scannerButton, isActive && styles.scannerButtonActive]}>
                      <Ionicons
                        name={isActive ? config.iconFocused : config.icon}
                        size={isActive ? 28 : 24}
                        color="white"
                      />
                    </View>
                  ) : (
                    <Ionicons
                      name={isActive ? config.iconFocused : config.icon}
                      size={isActive ? 28 : 24}
                      color={isActive ? config.color : '#9CA3AF'}
                    />
                  )}
                </Animated.View>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
  },
  solidContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 2,
    borderTopColor: '#E5E7EB',
    position: 'relative',
  },
  lineIndicator: {
    position: 'absolute',
    top: 0,
    height: 4,
    backgroundColor: '#6B7C5A',
    borderRadius: 2,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingTop: 16,
    paddingBottom: 16,
    paddingHorizontal: 8,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  scannerButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#5A6B4A',
  },
  scannerButtonActive: {
    backgroundColor: '#5A6B4A',
    borderColor: '#4A5B3A',
  },
});

export default AnimatedTabBar;
