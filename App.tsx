import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { View } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import * as Location from 'expo-location';
import { Pedometer } from 'expo-sensors';

// Import screens - using modern enhanced versions
import HomeScreen from './src/screens/HomeScreenModern';
import ScannerScreen from './src/screens/ScannerScreenModern';
import RecipesScreen from './src/screens/RecipesScreenModern';
import PlanScreen from './src/screens/PlanScreenModern';
import ProfileScreen from './src/screens/ProfileScreenNew';
import AskScreen from './src/screens/AskScreenModern';
import RecipeDetailScreen from './src/screens/RecipeDetailScreenModern';
import CookingTimerScreen from './src/screens/CookingTimerScreenModern';

// Import contexts
import { ProfileProvider, useProfile } from './src/contexts/ProfileContext';
import { OnboardingProvider } from './src/contexts/OnboardingContext';
import { AppStateProvider, useAppState } from './src/contexts/AppStateContext';

// Import onboarding
import OnboardingNavigator from './src/screens/onboarding/OnboardingNavigator';

// Import navigation components
import AnimatedTabBar from './src/components/navigation/AnimatedTabBar';
import SplashScreen from './src/components/SplashScreen';
import PermissionsScreen from './src/components/PermissionsScreen';

// Import services
import NotificationService from './src/services/NotificationService';

// Import icons
import { Ionicons } from '@expo/vector-icons';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

function TabNavigator() {
  return (
    <Tab.Navigator
      tabBar={(props) => <AnimatedTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Recipes" component={RecipesScreen} />
      <Tab.Screen name="Scanner" component={ScannerScreen} />
      <Tab.Screen name="Plan" component={PlanScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
}

function AppContent() {
  const { isOnboardingComplete } = useAppState();
  const { isProfileLoaded } = useProfile();
  const [showSplash, setShowSplash] = useState(true);
  const [showPermissions, setShowPermissions] = useState(false);
  const [permissionsChecked, setPermissionsChecked] = useState(false);

  // Initialize notification service
  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        const notificationService = NotificationService.getInstance();
        await notificationService.requestPermissions();

        // Schedule daily achievement notifications
        await notificationService.scheduleDailyAchievementNotification();

        console.log('✅ Notification service initialized');
      } catch (error) {
        console.error('❌ Failed to initialize notifications:', error);
      }
    };

    initializeNotifications();
  }, []);

  // Check if all critical permissions are granted (without requesting them)
  const checkPermissions = async () => {
    try {
      // Check permissions without requesting them
      const cameraStatus = await ImagePicker.getCameraPermissionsAsync();
      const audioStatus = await Audio.getPermissionsAsync();
      const mediaStatus = await ImagePicker.getMediaLibraryPermissionsAsync();
      const locationStatus = await Location.getForegroundPermissionsAsync();

      let pedometerGranted = false;
      const pedometerAvailable = await Pedometer.isAvailableAsync();
      if (pedometerAvailable) {
        const pedometerStatus = await Pedometer.getPermissionsAsync();
        pedometerGranted = pedometerStatus.status === 'granted';
      }

      const allGranted =
        cameraStatus.status === 'granted' &&
        audioStatus.status === 'granted' &&
        mediaStatus.status === 'granted' &&
        locationStatus.status === 'granted' &&
        pedometerGranted;

      setPermissionsChecked(true);
      setShowPermissions(!allGranted);
    } catch (error) {
      console.error('Error checking permissions:', error);
      setPermissionsChecked(true);
      setShowPermissions(true); // Show permissions screen if check fails
    }
  };

  // Show custom splash screen first
  if (showSplash) {
    return (
      <SplashScreen
        onAnimationComplete={() => {
          setShowSplash(false);
          checkPermissions();
        }}
      />
    );
  }

  // Wait for permissions check to complete
  if (!permissionsChecked) {
    return <View style={{ flex: 1, backgroundColor: '#6B7C5A' }} />;
  }

  // Show permissions screen only if permissions are missing
  if (showPermissions) {
    return (
      <PermissionsScreen
        onPermissionsGranted={() => setShowPermissions(false)}
      />
    );
  }

  // Show loading while checking onboarding status or loading profile
  if (isOnboardingComplete === null || !isProfileLoaded) {
    return <View style={{ flex: 1, backgroundColor: '#6B7C5A' }} />;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <NavigationContainer>
          {isOnboardingComplete ? (
              <Stack.Navigator
                screenOptions={{
                  headerShown: false,
                  presentation: 'card',
                  animationTypeForReplace: 'push',
                  cardStyleInterpolator: ({ current, layouts }) => {
                    return {
                      cardStyle: {
                        transform: [
                          {
                            translateX: current.progress.interpolate({
                              inputRange: [0, 1],
                              outputRange: [layouts.screen.width, 0],
                            }),
                          },
                        ],
                        opacity: current.progress.interpolate({
                          inputRange: [0, 0.5, 1],
                          outputRange: [0, 0.5, 1],
                        }),
                      },
                    };
                  },
                }}
              >
                <Stack.Screen
                  name="MainTabs"
                  component={TabNavigator}
                  options={{
                    headerShown: false
                  }}
                />
            <Stack.Screen
              name="Ask"
              component={AskScreen}
              options={{
                headerShown: false,
                presentation: 'modal',
                cardStyleInterpolator: ({ current, layouts }) => {
                  return {
                    cardStyle: {
                      transform: [
                        {
                          translateY: current.progress.interpolate({
                            inputRange: [0, 1],
                            outputRange: [layouts.screen.height, 0],
                          }),
                        },
                      ],
                    },
                  };
                },
              }}
            />
            <Stack.Screen
              name="RecipeDetail"
              component={RecipeDetailScreen}
              options={{
                headerShown: false,
                presentation: 'card',
              }}
            />
            <Stack.Screen
              name="CookingTimer"
              component={CookingTimerScreen}
              options={{
                headerShown: false,
                presentation: 'card',
              }}
            />
              </Stack.Navigator>
            ) : (
              <OnboardingNavigator />
            )}
          </NavigationContainer>
          <StatusBar style="dark" />
        </SafeAreaProvider>
      </GestureHandlerRootView>
    );
}

export default function App() {
  return (
    <AppStateProvider>
      <ProfileProvider>
        <OnboardingProvider>
          <AppContent />
        </OnboardingProvider>
      </ProfileProvider>
    </AppStateProvider>
  );
}
