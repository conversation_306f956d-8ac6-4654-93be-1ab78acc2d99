import AsyncStorage from '@react-native-async-storage/async-storage';
import SecurityManager from './SecurityManager';
import AdvancedDataManager from './AdvancedDataManager';
import { PerformanceMonitor } from '../utils/PerformanceOptimizations';

// Enhanced AI service with advanced capabilities
export class EnhancedAIService {
  private static instance: EnhancedAIService;
  private securityManager: SecurityManager;
  private dataManager: AdvancedDataManager;
  private performanceMonitor: PerformanceMonitor;
  private apiKey: string | null = null;
  private modelCache: Map<string, any> = new Map();
  private userPreferences: UserPreferences | null = null;
  private userHistory: UserInteraction[] = [];
  private maxHistoryItems: number = 50;

  static getInstance(): EnhancedAIService {
    if (!EnhancedAIService.instance) {
      EnhancedAIService.instance = new EnhancedAIService();
    }
    return EnhancedAIService.instance;
  }

  constructor() {
    this.securityManager = SecurityManager.getInstance();
    this.dataManager = AdvancedDataManager.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      // Initialize security manager
      await this.securityManager.initialize();
      
      // Load API key securely
      this.apiKey = await this.securityManager.getAPIKey('gemini');
      
      // Load user preferences
      this.userPreferences = await this.dataManager.getItem<UserPreferences>('ai_preferences');
      
      // Load interaction history
      const history = await this.dataManager.getItem<UserInteraction[]>('ai_interaction_history');
      if (history) {
        this.userHistory = history;
      }
      
      console.log('✅ Enhanced AI Service initialized successfully');
    } catch (error) {
      console.error('❌ Enhanced AI Service initialization failed:', error);
    }
  }

  // Personalized meal recommendations with context awareness
  async getPersonalizedMealRecommendations(
    userProfile: any,
    options: MealRecommendationOptions
  ): Promise<MealRecommendation[]> {
    this.performanceMonitor.startTimer('personalized_meal_recommendations');
    
    try {
      // Check cache first
      const cacheKey = `meal_recommendations_${JSON.stringify(options)}`;
      const cachedRecommendations = await this.dataManager.getCached<MealRecommendation[]>(cacheKey);
      
      if (cachedRecommendations) {
        console.log('✅ Using cached meal recommendations');
        return this.personalizeResults(cachedRecommendations, userProfile);
      }
      
      // Build context-aware prompt
      const prompt = this.buildPersonalizedPrompt(userProfile, options, 'meal_recommendations');
      
      // Call AI API with enhanced error handling
      const response = await this.callGeminiAPI(prompt, {
        temperature: 0.7,
        maxTokens: 1024,
        topK: 40,
        topP: 0.95,
      });
      
      // Parse and validate response
      const recommendations = this.parseAndValidateResponse<MealRecommendation[]>(
        response,
        'meal_recommendations'
      );
      
      // Cache results
      await this.dataManager.setCached(cacheKey, recommendations, 60); // 1 hour TTL
      
      // Track interaction
      this.trackInteraction('meal_recommendations', options, recommendations);
      
      // Return personalized results
      return this.personalizeResults(recommendations, userProfile);
    } catch (error) {
      console.error('❌ Personalized meal recommendations failed:', error);
      throw error;
    } finally {
      this.performanceMonitor.endTimer('personalized_meal_recommendations');
    }
  }

  // Advanced nutrition analysis with image recognition
  async analyzeNutritionFromImage(
    imageBase64: string,
    options: NutritionAnalysisOptions
  ): Promise<NutritionAnalysis> {
    this.performanceMonitor.startTimer('nutrition_analysis');
    
    try {
      // Build multimodal prompt
      const prompt = this.buildMultimodalPrompt(imageBase64, options);
      
      // Call AI API with enhanced error handling
      const response = await this.callGeminiVisionAPI(prompt, imageBase64, {
        temperature: 0.2, // Lower temperature for more accurate analysis
        maxTokens: 1024,
      });
      
      // Parse and validate response
      const analysis = this.parseAndValidateResponse<NutritionAnalysis>(
        response,
        'nutrition_analysis'
      );
      
      // Track interaction
      this.trackInteraction('nutrition_analysis', options, analysis);
      
      return analysis;
    } catch (error) {
      console.error('❌ Nutrition analysis failed:', error);
      throw error;
    } finally {
      this.performanceMonitor.endTimer('nutrition_analysis');
    }
  }

  // Personalized health insights with trend analysis
  async getHealthInsights(
    healthData: any,
    userProfile: any,
    options: HealthInsightOptions
  ): Promise<HealthInsight[]> {
    this.performanceMonitor.startTimer('health_insights');
    
    try {
      // Analyze trends in health data
      const trends = this.analyzeTrends(healthData);
      
      // Build context-aware prompt
      const prompt = this.buildPersonalizedPrompt(
        { ...userProfile, healthData, trends },
        options,
        'health_insights'
      );
      
      // Call AI API with enhanced error handling
      const response = await this.callGeminiAPI(prompt, {
        temperature: 0.3,
        maxTokens: 1024,
      });
      
      // Parse and validate response
      const insights = this.parseAndValidateResponse<HealthInsight[]>(
        response,
        'health_insights'
      );
      
      // Track interaction
      this.trackInteraction('health_insights', options, insights);
      
      return insights;
    } catch (error) {
      console.error('❌ Health insights failed:', error);
      throw error;
    } finally {
      this.performanceMonitor.endTimer('health_insights');
    }
  }

  // Adaptive meal planning with user feedback
  async generateAdaptiveMealPlan(
    userProfile: any,
    previousFeedback: MealFeedback[],
    options: MealPlanOptions
  ): Promise<MealPlan> {
    this.performanceMonitor.startTimer('adaptive_meal_plan');
    
    try {
      // Incorporate user feedback
      const adaptedOptions = this.incorporateUserFeedback(options, previousFeedback);
      
      // Build context-aware prompt
      const prompt = this.buildPersonalizedPrompt(
        userProfile,
        adaptedOptions,
        'meal_plan'
      );
      
      // Call AI API with enhanced error handling
      const response = await this.callGeminiAPI(prompt, {
        temperature: 0.7,
        maxTokens: 2048,
      });
      
      // Parse and validate response
      const mealPlan = this.parseAndValidateResponse<MealPlan>(
        response,
        'meal_plan'
      );
      
      // Track interaction
      this.trackInteraction('meal_plan', adaptedOptions, mealPlan);
      
      return mealPlan;
    } catch (error) {
      console.error('❌ Adaptive meal plan failed:', error);
      throw error;
    } finally {
      this.performanceMonitor.endTimer('adaptive_meal_plan');
    }
  }

  // Helper methods
  private buildPersonalizedPrompt(
    userProfile: any,
    options: any,
    promptType: string
  ): string {
    // Implement sophisticated prompt engineering based on prompt type
    let basePrompt = '';
    
    switch (promptType) {
      case 'meal_recommendations':
        basePrompt = `You are a professional nutritionist creating personalized meal recommendations. 
        The user has the following profile: ${JSON.stringify(userProfile)}.
        They are looking for meals with these criteria: ${JSON.stringify(options)}.
        Provide 5 highly personalized meal recommendations in JSON format.`;
        break;
      case 'health_insights':
        basePrompt = `You are a health analytics expert analyzing health data trends.
        User profile: ${JSON.stringify(userProfile)}.
        Provide actionable health insights based on their data in JSON format.`;
        break;
      case 'meal_plan':
        basePrompt = `You are a meal planning expert creating a personalized weekly meal plan.
        User profile: ${JSON.stringify(userProfile)}.
        Plan requirements: ${JSON.stringify(options)}.
        Create a detailed 7-day meal plan in JSON format.`;
        break;
      default:
        basePrompt = `You are a nutrition AI assistant helping with ${promptType}.
        User profile: ${JSON.stringify(userProfile)}.
        Respond in JSON format.`;
    }
    
    // Add user preferences if available
    if (this.userPreferences) {
      basePrompt += `\nConsider these user preferences: ${JSON.stringify(this.userPreferences)}`;
    }
    
    // Add recent interactions for context
    if (this.userHistory.length > 0) {
      const recentHistory = this.userHistory.slice(-3);
      basePrompt += `\nRecent interactions: ${JSON.stringify(recentHistory)}`;
    }
    
    return basePrompt;
  }

  private buildMultimodalPrompt(imageBase64: string, options: any): string {
    return `Analyze this food image and provide detailed nutrition information.
    Include: calories, macronutrients (protein, carbs, fat), estimated portion size, and ingredients.
    Additional requirements: ${JSON.stringify(options)}.
    Respond in JSON format.`;
  }

  private async callGeminiAPI(prompt: string, options: any): Promise<string> {
    if (!this.apiKey) {
      throw new Error('API key not initialized');
    }
    
    try {
      const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
          ...this.securityManager.getSecureHeaders(),
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: options.temperature || 0.7,
            maxOutputTokens: options.maxTokens || 1024,
            topK: options.topK || 40,
            topP: options.topP || 0.95,
          },
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(`Gemini API error: ${data.error?.message || response.statusText}`);
      }
      
      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('❌ Gemini API call failed:', error);
      throw error;
    }
  }

  private async callGeminiVisionAPI(prompt: string, imageBase64: string, options: any): Promise<string> {
    if (!this.apiKey) {
      throw new Error('API key not initialized');
    }
    
    try {
      const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
          ...this.securityManager.getSecureHeaders(),
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: prompt
                },
                {
                  inlineData: {
                    mimeType: 'image/jpeg',
                    data: imageBase64
                  }
                }
              ]
            }
          ],
          generationConfig: {
            temperature: options.temperature || 0.7,
            maxOutputTokens: options.maxTokens || 1024,
          },
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(`Gemini Vision API error: ${data.error?.message || response.statusText}`);
      }
      
      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      console.error('❌ Gemini Vision API call failed:', error);
      throw error;
    }
  }

  private parseAndValidateResponse<T>(response: string, responseType: string): T {
    try {
      // Clean JSON string
      const cleanedResponse = response
        .replace(/```json/g, '')
        .replace(/```/g, '')
        .trim();
      
      // Parse JSON
      const parsed = JSON.parse(cleanedResponse);
      
      // Validate schema based on response type
      this.validateSchema(parsed, responseType);
      
      return parsed as T;
    } catch (error) {
      console.error(`❌ Failed to parse ${responseType} response:`, error);
      throw new Error(`Invalid AI response format for ${responseType}`);
    }
  }

  private validateSchema(data: any, schemaType: string): boolean {
    // Implement schema validation based on response type
    // This would be more sophisticated in production
    switch (schemaType) {
      case 'meal_recommendations':
        return Array.isArray(data);
      case 'nutrition_analysis':
        return data && typeof data === 'object' && 'calories' in data;
      case 'health_insights':
        return Array.isArray(data);
      case 'meal_plan':
        return data && typeof data === 'object' && 'week' in data;
      default:
        return true;
    }
  }

  private personalizeResults<T>(results: T, userProfile: any): T {
    // Implement personalization logic
    // This would be more sophisticated in production
    return results;
  }

  private analyzeTrends(healthData: any): any {
    // Implement trend analysis
    // This would be more sophisticated in production
    return {
      trends: 'analyzed',
    };
  }

  private incorporateUserFeedback(options: any, feedback: MealFeedback[]): any {
    // Implement feedback incorporation
    // This would be more sophisticated in production
    return {
      ...options,
      feedback: feedback,
    };
  }

  private trackInteraction(type: string, input: any, output: any): void {
    const interaction: UserInteraction = {
      type,
      timestamp: Date.now(),
      input: JSON.stringify(input).substring(0, 100), // Truncate for storage efficiency
      outputSummary: JSON.stringify(output).substring(0, 100), // Truncate for storage efficiency
    };
    
    this.userHistory.push(interaction);
    
    // Maintain max history size
    if (this.userHistory.length > this.maxHistoryItems) {
      this.userHistory = this.userHistory.slice(-this.maxHistoryItems);
    }
    
    // Persist history
    this.dataManager.setItem('ai_interaction_history', this.userHistory);
  }

  // User preference management
  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<void> {
    this.userPreferences = {
      ...this.userPreferences,
      ...preferences,
    };
    
    await this.dataManager.setItem('ai_preferences', this.userPreferences);
  }

  async getUserPreferences(): Promise<UserPreferences | null> {
    return this.userPreferences;
  }
}

// Type definitions
interface UserPreferences {
  responseFormat: 'detailed' | 'concise';
  mealPreferences: string[];
  healthFocus: string[];
  aiPersonality: 'professional' | 'friendly' | 'motivational';
}

interface UserInteraction {
  type: string;
  timestamp: number;
  input: string;
  outputSummary: string;
}

interface MealRecommendationOptions {
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  calorieRange: [number, number];
  preparationTime?: number;
  ingredients?: string[];
  excludeIngredients?: string[];
}

interface MealRecommendation {
  id: string;
  name: string;
  description: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  preparationTime: number;
  ingredients: string[];
  instructions: string[];
  imagePrompt: string;
}

interface NutritionAnalysisOptions {
  detailLevel: 'basic' | 'detailed';
  includeMacronutrients: boolean;
  includeMicronutrients: boolean;
}

interface NutritionAnalysis {
  foodName: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  sugar: number;
  portionSize: string;
  ingredients: string[];
  allergens: string[];
  confidence: number;
}

interface HealthInsightOptions {
  timeRange: 'day' | 'week' | 'month';
  focusAreas: string[];
}

interface HealthInsight {
  type: string;
  insight: string;
  recommendation: string;
  priority: 'high' | 'medium' | 'low';
}

interface MealPlanOptions {
  days: number;
  mealsPerDay: number;
  calorieTarget: number;
  proteinTarget: number;
  dietaryRestrictions: string[];
}

interface MealPlan {
  week: any[];
}

interface MealFeedback {
  mealId: string;
  rating: number;
  comments: string;
  timestamp: number;
}

export default EnhancedAIService;
