<!DOCTYPE html>
<html>
<head>
    <title>NutriAI Icon Generator</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px;
        }
        canvas { 
            border: 1px solid #ddd; 
            margin: 10px; 
            border-radius: 8px;
        }
        .icon-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin-top: 20px;
        }
        .icon-item { 
            text-align: center; 
            padding: 15px; 
            background: #f9f9f9; 
            border-radius: 8px;
        }
        button { 
            background: #6B7C5A; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px;
        }
        button:hover { 
            background: #5a6b4a; 
        }
        .instructions {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #6B7C5A;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍃 NutriAI App Icon Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Generate Icons" to create all required app icons</li>
                <li>Download each icon by right-clicking and "Save image as..."</li>
                <li>Save them in your assets folder with the exact names shown</li>
                <li>Rebuild your app to see the custom icons</li>
            </ol>
        </div>

        <button onclick="generateAllIcons()">🎨 Generate All Icons</button>
        <button onclick="downloadAll()">📥 Download All Icons</button>

        <div class="icon-grid" id="iconGrid">
            <!-- Icons will be generated here -->
        </div>
    </div>

    <script>


        function generateAllIcons() {
            const iconGrid = document.getElementById('iconGrid');
            iconGrid.innerHTML = '';

            const icons = [
                { size: 1024, name: 'icon.png', description: 'Main App Icon (1024x1024) - White BG', bg: '#ffffff' },
                { size: 1024, name: 'adaptive-icon.png', description: 'Android Adaptive Icon (1024x1024) - Transparent', bg: 'transparent' },
                { size: 1024, name: 'splash-icon.png', description: 'Splash Screen Icon (1024x1024) - Transparent', bg: 'transparent' }
            ];

            // Load the logo image once
            const logoImg = new Image();
            logoImg.onload = function() {
                icons.forEach((iconConfig, index) => {
                    const canvas = document.createElement('canvas');
                    canvas.width = iconConfig.size;
                    canvas.height = iconConfig.size;
                    const ctx = canvas.getContext('2d');

                    // Add background only for main icon
                    if (iconConfig.bg !== 'transparent') {
                        ctx.fillStyle = iconConfig.bg;
                        ctx.fillRect(0, 0, iconConfig.size, iconConfig.size);
                    }

                    // Calculate scaling to fit the logo nicely in the icon
                    const logoScale = iconConfig.size * 0.8; // Use 80% of icon size
                    const logoWidth = logoScale;
                    const logoHeight = (logoImg.height / logoImg.width) * logoScale;

                    // Center the logo
                    const x = (iconConfig.size - logoWidth) / 2;
                    const y = (iconConfig.size - logoHeight) / 2;

                    // Draw the logo
                    ctx.drawImage(logoImg, x, y, logoWidth, logoHeight);

                    // Create UI elements
                    const iconItem = document.createElement('div');
                    iconItem.className = 'icon-item';

                    const title = document.createElement('h3');
                    title.textContent = iconConfig.name;

                    const description = document.createElement('p');
                    description.textContent = iconConfig.description;

                    const downloadBtn = document.createElement('button');
                    downloadBtn.textContent = `Download ${iconConfig.name}`;
                    downloadBtn.onclick = () => downloadCanvas(canvas, iconConfig.name);

                    // Create display canvas
                    const displayCanvas = document.createElement('canvas');
                    displayCanvas.width = 150;
                    displayCanvas.height = 150;
                    const displayCtx = displayCanvas.getContext('2d');
                    displayCtx.drawImage(canvas, 0, 0, 150, 150);

                    iconItem.appendChild(title);
                    iconItem.appendChild(displayCanvas);
                    iconItem.appendChild(description);
                    iconItem.appendChild(downloadBtn);

                    iconGrid.appendChild(iconItem);
                });
            };

            logoImg.onerror = function() {
                iconGrid.innerHTML = '<p style="color: red; text-align: center;">❌ Could not load logo image: assets/image final.png<br>Make sure the file exists in the assets folder.</p>';
            };

            // Load your specific logo file
            logoImg.src = './assets/image final.png';
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function downloadAll() {
            const canvases = document.querySelectorAll('canvas');
            const names = ['icon.png', 'adaptive-icon.png', 'splash-icon.png'];
            
            canvases.forEach((canvas, index) => {
                if (names[index]) {
                    setTimeout(() => downloadCanvas(canvas, names[index]), index * 500);
                }
            });
        }

        // Generate icons on page load
        window.onload = generateAllIcons;
    </script>
</body>
</html>
