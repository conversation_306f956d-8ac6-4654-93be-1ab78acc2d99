import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo/netinfo';

// Advanced data management with offline-first architecture
export class AdvancedDataManager {
  private static instance: AdvancedDataManager;
  private syncQueue: SyncOperation[] = [];
  private isOnline: boolean = true;
  private syncInProgress: boolean = false;

  static getInstance(): AdvancedDataManager {
    if (!AdvancedDataManager.instance) {
      AdvancedDataManager.instance = new AdvancedDataManager();
    }
    return AdvancedDataManager.instance;
  }

  constructor() {
    this.initializeNetworkListener();
    this.initializePeriodicSync();
  }

  private initializeNetworkListener(): void {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      if (wasOffline && this.isOnline) {
        console.log('🌐 Back online - starting sync');
        this.processSyncQueue();
      }
    });
  }

  private initializePeriodicSync(): void {
    // Sync every 5 minutes when online
    setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.processSyncQueue();
      }
    }, 5 * 60 * 1000);
  }

  // Optimized storage with compression
  async setItem(key: string, value: any, options?: StorageOptions): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      const compressed = options?.compress ? this.compress(serialized) : serialized;
      
      await AsyncStorage.setItem(key, compressed);
      
      // Add to sync queue if requires server sync
      if (options?.syncToServer && this.isOnline) {
        this.addToSyncQueue({
          type: 'update',
          key,
          data: value,
          timestamp: Date.now(),
          priority: options.priority || 'normal'
        });
      }
    } catch (error) {
      console.error(`Storage error for key ${key}:`, error);
      throw error;
    }
  }

  async getItem<T>(key: string, options?: StorageOptions): Promise<T | null> {
    try {
      const stored = await AsyncStorage.getItem(key);
      if (!stored) return null;
      
      const decompressed = options?.compress ? this.decompress(stored) : stored;
      return JSON.parse(decompressed);
    } catch (error) {
      console.error(`Retrieval error for key ${key}:`, error);
      return null;
    }
  }

  // Batch operations for better performance
  async batchSet(operations: BatchOperation[]): Promise<void> {
    const pairs: [string, string][] = [];
    
    for (const op of operations) {
      const serialized = JSON.stringify(op.value);
      const compressed = op.options?.compress ? this.compress(serialized) : serialized;
      pairs.push([op.key, compressed]);
      
      // Add to sync queue if needed
      if (op.options?.syncToServer && this.isOnline) {
        this.addToSyncQueue({
          type: 'update',
          key: op.key,
          data: op.value,
          timestamp: Date.now(),
          priority: op.options.priority || 'normal'
        });
      }
    }
    
    await AsyncStorage.multiSet(pairs);
  }

  async batchGet(keys: string[]): Promise<Record<string, any>> {
    const pairs = await AsyncStorage.multiGet(keys);
    const result: Record<string, any> = {};
    
    for (const [key, value] of pairs) {
      if (value) {
        try {
          result[key] = JSON.parse(value);
        } catch (error) {
          console.warn(`Parse error for key ${key}:`, error);
        }
      }
    }
    
    return result;
  }

  // Smart caching with TTL
  async setCached(key: string, value: any, ttlMinutes: number = 60): Promise<void> {
    const cacheData = {
      value,
      timestamp: Date.now(),
      ttl: ttlMinutes * 60 * 1000
    };
    
    await this.setItem(`cache_${key}`, cacheData);
  }

  async getCached<T>(key: string): Promise<T | null> {
    const cacheData = await this.getItem<CacheData>(`cache_${key}`);
    
    if (!cacheData) return null;
    
    const isExpired = Date.now() - cacheData.timestamp > cacheData.ttl;
    if (isExpired) {
      await AsyncStorage.removeItem(`cache_${key}`);
      return null;
    }
    
    return cacheData.value;
  }

  // Sync queue management
  private addToSyncQueue(operation: SyncOperation): void {
    this.syncQueue.push(operation);
    this.syncQueue.sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  private async processSyncQueue(): Promise<void> {
    if (this.syncInProgress || !this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    console.log(`🔄 Processing ${this.syncQueue.length} sync operations`);

    const batch = this.syncQueue.splice(0, 10); // Process in batches of 10
    
    try {
      await Promise.all(batch.map(op => this.executeSyncOperation(op)));
      console.log('✅ Sync batch completed successfully');
    } catch (error) {
      console.error('❌ Sync batch failed:', error);
      // Re-add failed operations to queue
      this.syncQueue.unshift(...batch);
    } finally {
      this.syncInProgress = false;
      
      // Continue processing if more items in queue
      if (this.syncQueue.length > 0) {
        setTimeout(() => this.processSyncQueue(), 1000);
      }
    }
  }

  private async executeSyncOperation(operation: SyncOperation): Promise<void> {
    // Implement actual server sync logic here
    console.log(`Syncing ${operation.type} for ${operation.key}`);
    // This would integrate with your backend API
  }

  // Data compression utilities
  private compress(data: string): string {
    // Simple compression - in production use a proper compression library
    return btoa(data);
  }

  private decompress(data: string): string {
    return atob(data);
  }

  // Analytics and monitoring
  async getStorageStats(): Promise<StorageStats> {
    const keys = await AsyncStorage.getAllKeys();
    let totalSize = 0;
    let itemCount = 0;
    
    for (const key of keys) {
      const value = await AsyncStorage.getItem(key);
      if (value) {
        totalSize += value.length;
        itemCount++;
      }
    }
    
    return {
      totalSize,
      itemCount,
      keys: keys.length,
      syncQueueSize: this.syncQueue.length,
      isOnline: this.isOnline
    };
  }
}

// Type definitions
interface StorageOptions {
  compress?: boolean;
  syncToServer?: boolean;
  priority?: 'high' | 'normal' | 'low';
}

interface BatchOperation {
  key: string;
  value: any;
  options?: StorageOptions;
}

interface SyncOperation {
  type: 'create' | 'update' | 'delete';
  key: string;
  data: any;
  timestamp: number;
  priority: 'high' | 'normal' | 'low';
}

interface CacheData {
  value: any;
  timestamp: number;
  ttl: number;
}

interface StorageStats {
  totalSize: number;
  itemCount: number;
  keys: number;
  syncQueueSize: number;
  isOnline: boolean;
}

export default AdvancedDataManager;
