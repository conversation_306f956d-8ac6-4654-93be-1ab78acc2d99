import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';
import * as SecureStore from 'expo-secure-store';
import * as LocalAuthentication from 'expo-local-authentication';
import { Platform } from 'react-native';

export class SecurityManager {
  private static instance: SecurityManager;
  private encryptionKey: string | null = null;
  private isInitialized: boolean = false;

  static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Generate or retrieve encryption key
      await this.initializeEncryption();
      
      // Setup biometric authentication if available
      await this.setupBiometricAuth();
      
      this.isInitialized = true;
      console.log('🔒 Security Manager initialized successfully');
    } catch (error) {
      console.error('❌ Security Manager initialization failed:', error);
      throw error;
    }
  }

  private async initializeEncryption(): Promise<void> {
    try {
      // Try to get existing key from secure store
      this.encryptionKey = await SecureStore.getItemAsync('encryption_key');
      
      if (!this.encryptionKey) {
        // Generate new encryption key
        this.encryptionKey = await this.generateEncryptionKey();
        await SecureStore.setItemAsync('encryption_key', this.encryptionKey);
        console.log('🔑 New encryption key generated and stored securely');
      }
    } catch (error) {
      console.error('❌ Encryption initialization failed:', error);
      throw error;
    }
  }

  private async generateEncryptionKey(): Promise<string> {
    const randomBytes = await Crypto.getRandomBytesAsync(32);
    return Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // Secure data storage with encryption
  async setSecureItem(key: string, value: any): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const serialized = JSON.stringify(value);
      const encrypted = await this.encrypt(serialized);
      await SecureStore.setItemAsync(key, encrypted);
    } catch (error) {
      console.error(`❌ Secure storage failed for key ${key}:`, error);
      throw error;
    }
  }

  async getSecureItem<T>(key: string): Promise<T | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const encrypted = await SecureStore.getItemAsync(key);
      if (!encrypted) return null;

      const decrypted = await this.decrypt(encrypted);
      return JSON.parse(decrypted);
    } catch (error) {
      console.error(`❌ Secure retrieval failed for key ${key}:`, error);
      return null;
    }
  }

  // API key management
  async storeAPIKey(service: string, apiKey: string): Promise<void> {
    await this.setSecureItem(`api_key_${service}`, apiKey);
  }

  async getAPIKey(service: string): Promise<string | null> {
    return await this.getSecureItem<string>(`api_key_${service}`);
  }

  // Biometric authentication
  private async setupBiometricAuth(): Promise<void> {
    const hasHardware = await LocalAuthentication.hasHardwareAsync();
    const isEnrolled = await LocalAuthentication.isEnrolledAsync();
    
    if (hasHardware && isEnrolled) {
      console.log('✅ Biometric authentication available');
      await AsyncStorage.setItem('biometric_available', 'true');
    } else {
      console.log('⚠️ Biometric authentication not available');
      await AsyncStorage.setItem('biometric_available', 'false');
    }
  }

  async authenticateWithBiometrics(reason: string = 'Authenticate to access your data'): Promise<boolean> {
    try {
      const biometricAvailable = await AsyncStorage.getItem('biometric_available');
      if (biometricAvailable !== 'true') {
        return false;
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: reason,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Passcode',
        disableDeviceFallback: false,
      });

      return result.success;
    } catch (error) {
      console.error('❌ Biometric authentication failed:', error);
      return false;
    }
  }

  // Data encryption/decryption
  private async encrypt(data: string): Promise<string> {
    if (!this.encryptionKey) {
      throw new Error('Encryption key not initialized');
    }

    // Simple encryption - in production use proper encryption library
    const digest = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      data + this.encryptionKey
    );
    
    return btoa(data) + '.' + digest.substring(0, 16);
  }

  private async decrypt(encryptedData: string): Promise<string> {
    if (!this.encryptionKey) {
      throw new Error('Encryption key not initialized');
    }

    const [encodedData, hash] = encryptedData.split('.');
    const data = atob(encodedData);
    
    // Verify integrity
    const expectedDigest = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      data + this.encryptionKey
    );
    
    if (expectedDigest.substring(0, 16) !== hash) {
      throw new Error('Data integrity check failed');
    }
    
    return data;
  }

  // Session management
  async createSecureSession(userId: string): Promise<string> {
    const sessionToken = await Crypto.getRandomBytesAsync(32);
    const sessionId = Array.from(sessionToken, byte => 
      byte.toString(16).padStart(2, '0')
    ).join('');
    
    const sessionData = {
      userId,
      createdAt: Date.now(),
      expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
    };
    
    await this.setSecureItem(`session_${sessionId}`, sessionData);
    return sessionId;
  }

  async validateSession(sessionId: string): Promise<boolean> {
    const sessionData = await this.getSecureItem<any>(`session_${sessionId}`);
    
    if (!sessionData) return false;
    if (Date.now() > sessionData.expiresAt) {
      await SecureStore.deleteItemAsync(`session_${sessionId}`);
      return false;
    }
    
    return true;
  }

  // Security audit
  async performSecurityAudit(): Promise<SecurityAuditResult> {
    const audit: SecurityAuditResult = {
      encryptionEnabled: !!this.encryptionKey,
      biometricAvailable: await AsyncStorage.getItem('biometric_available') === 'true',
      secureStoreAvailable: await SecureStore.isAvailableAsync(),
      recommendations: []
    };

    // Add recommendations based on audit
    if (!audit.encryptionEnabled) {
      audit.recommendations.push('Enable data encryption');
    }
    
    if (!audit.biometricAvailable) {
      audit.recommendations.push('Setup biometric authentication');
    }

    return audit;
  }

  // Secure network requests
  getSecureHeaders(): Record<string, string> {
    return {
      'X-App-Version': '1.0.0',
      'X-Platform': Platform.OS,
      'X-Timestamp': Date.now().toString(),
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  // Data sanitization
  sanitizeInput(input: string): string {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/[<>]/g, '')
      .trim();
  }

  // Rate limiting
  private rateLimitMap: Map<string, number[]> = new Map();

  isRateLimited(identifier: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
    const now = Date.now();
    const requests = this.rateLimitMap.get(identifier) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < windowMs);
    
    if (validRequests.length >= maxRequests) {
      return true;
    }
    
    validRequests.push(now);
    this.rateLimitMap.set(identifier, validRequests);
    return false;
  }
}

interface SecurityAuditResult {
  encryptionEnabled: boolean;
  biometricAvailable: boolean;
  secureStoreAvailable: boolean;
  recommendations: string[];
}

export default SecurityManager;
