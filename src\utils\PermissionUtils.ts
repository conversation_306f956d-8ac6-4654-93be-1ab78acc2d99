import { Platform, PermissionsAndroid, Alert, Linking } from 'react-native';
// Removed react-native-permissions due to build issues
// Using only native Android methods and Expo APIs
import { 
  Pedometer, 
  Accelerometer, 
  Gyroscope, 
  Magnetometer, 
  Barometer, 
  LightSensor 
} from 'expo-sensors';

export interface PermissionResult {
  granted: boolean;
  method: string;
  androidVersion: number;
  availableSensors: string[];
  grantedPermissions: string[];
}

export class PermissionUtils {
  
  /**
   * Check health sensor permissions across all Android versions
   */
  static async checkHealthSensorPermissions(): Promise<PermissionResult> {
    const result: PermissionResult = {
      granted: false,
      method: 'none',
      androidVersion: Platform.Version as number,
      availableSensors: [],
      grantedPermissions: []
    };

    if (Platform.OS !== 'android') {
      result.granted = true;
      result.method = 'ios';
      return result;
    }

    console.log(`🔍 Checking health permissions for Android ${Platform.Version}`);

    // Check available sensors first
    const sensorChecks = await Promise.all([
      Pedometer.isAvailableAsync().catch(() => false),
      Accelerometer.isAvailableAsync().catch(() => false),
      Gyroscope.isAvailableAsync().catch(() => false),
      Magnetometer.isAvailableAsync().catch(() => false),
      Barometer.isAvailableAsync().catch(() => false),
      LightSensor.isAvailableAsync().catch(() => false)
    ]);

    const sensorNames = ['Pedometer', 'Accelerometer', 'Gyroscope', 'Magnetometer', 'Barometer', 'LightSensor'];
    result.availableSensors = sensorNames.filter((_, index) => sensorChecks[index]);

    // Method 1: Try native PermissionsAndroid (primary method)
    try {
      const nativePermissions = [PermissionsAndroid.PERMISSIONS.BODY_SENSORS];
      if (Platform.Version >= 29) {
        nativePermissions.push(PermissionsAndroid.PERMISSIONS.ACTIVITY_RECOGNITION);
      }

      const results = await Promise.all(
        nativePermissions.map(p => PermissionsAndroid.check(p))
      );

      const hasGranted = results.some(granted => granted);
      if (hasGranted) {
        result.granted = true;
        result.method = 'native-android';
        result.grantedPermissions = nativePermissions.filter((_, index) => results[index]);
        return result;
      }
    } catch (error) {
      console.log('⚠️ Native Android permission check failed:', error);
    }

    // Method 2: Try Expo sensor permissions
    try {
      if (sensorChecks[0]) { // Pedometer available
        const pedometerStatus = await Pedometer.getPermissionsAsync();
        if (pedometerStatus.status === 'granted') {
          result.granted = true;
          result.method = 'expo-pedometer';
          result.grantedPermissions = ['expo-pedometer'];
          return result;
        }
      }
    } catch (error) {
      console.log('⚠️ Expo sensor permission check failed:', error);
    }

    return result;
  }

  /**
   * Request health sensor permissions using multiple methods
   */
  static async requestHealthSensorPermissions(): Promise<PermissionResult> {
    const result: PermissionResult = {
      granted: false,
      method: 'none',
      androidVersion: Platform.Version as number,
      availableSensors: [],
      grantedPermissions: []
    };

    if (Platform.OS !== 'android') {
      result.granted = true;
      result.method = 'ios';
      return result;
    }

    console.log(`🔄 Requesting health permissions for Android ${Platform.Version}`);

    // Check current status first
    const currentStatus = await this.checkHealthSensorPermissions();
    if (currentStatus.granted) {
      return currentStatus;
    }

    result.availableSensors = currentStatus.availableSensors;

    // Method 1: Try native PermissionsAndroid (primary method)
    try {
      const nativePermissions = [PermissionsAndroid.PERMISSIONS.BODY_SENSORS];
      if (Platform.Version >= 29) {
        nativePermissions.push(PermissionsAndroid.PERMISSIONS.ACTIVITY_RECOGNITION);
      }

      const results = await PermissionsAndroid.requestMultiple(nativePermissions);
      const hasGranted = Object.values(results).some(status => status === PermissionsAndroid.RESULTS.GRANTED);

      if (hasGranted) {
        result.granted = true;
        result.method = 'native-android';
        result.grantedPermissions = Object.keys(results).filter(key => results[key] === PermissionsAndroid.RESULTS.GRANTED);
        return result;
      }
    } catch (error) {
      console.log('⚠️ Native Android permission request failed:', error);
    }

    // Method 2: Try Expo sensors
    try {
      if (result.availableSensors.includes('Pedometer')) {
        const pedometerResult = await Pedometer.requestPermissionsAsync();
        if (pedometerResult.status === 'granted') {
          result.granted = true;
          result.method = 'expo-pedometer';
          result.grantedPermissions = ['expo-pedometer'];
          return result;
        }
      }
    } catch (error) {
      console.log('⚠️ Expo sensor permission request failed:', error);
    }

    return result;
  }

  /**
   * Show permission denied dialog with settings option
   */
  static showPermissionDeniedDialog(permissionType: string, androidVersion: number): void {
    Alert.alert(
      `${permissionType} Permission Required`,
      `${permissionType} permissions are required for health monitoring. Please enable them in Settings.\n\nAndroid ${androidVersion} detected - multiple permission methods were attempted.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open Settings',
          onPress: () => Linking.openSettings()
        }
      ]
    );
  }

  /**
   * Get permission status summary for debugging
   */
  static async getPermissionStatusSummary(): Promise<string> {
    const healthStatus = await this.checkHealthSensorPermissions();
    
    return `
Android Version: ${Platform.Version}
Health Permissions: ${healthStatus.granted ? 'GRANTED' : 'DENIED'}
Method Used: ${healthStatus.method}
Available Sensors: ${healthStatus.availableSensors.join(', ')}
Granted Permissions: ${healthStatus.grantedPermissions.join(', ')}
    `.trim();
  }
}
