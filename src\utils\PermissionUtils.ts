import { Platform, PermissionsAndroid, Alert, Linking } from 'react-native';
import { 
  request, 
  check, 
  PERMISSIONS, 
  RESULTS, 
  requestMultiple,
  openSettings 
} from 'react-native-permissions';
import { 
  Pedometer, 
  Accelerometer, 
  Gyroscope, 
  Magnetometer, 
  Barometer, 
  LightSensor 
} from 'expo-sensors';

export interface PermissionResult {
  granted: boolean;
  method: string;
  androidVersion: number;
  availableSensors: string[];
  grantedPermissions: string[];
}

export class PermissionUtils {
  
  /**
   * Check health sensor permissions across all Android versions
   */
  static async checkHealthSensorPermissions(): Promise<PermissionResult> {
    const result: PermissionResult = {
      granted: false,
      method: 'none',
      androidVersion: Platform.Version as number,
      availableSensors: [],
      grantedPermissions: []
    };

    if (Platform.OS !== 'android') {
      result.granted = true;
      result.method = 'ios';
      return result;
    }

    console.log(`🔍 Checking health permissions for Android ${Platform.Version}`);

    // Check available sensors first
    const sensorChecks = await Promise.all([
      Pedometer.isAvailableAsync().catch(() => false),
      Accelerometer.isAvailableAsync().catch(() => false),
      Gyroscope.isAvailableAsync().catch(() => false),
      Magnetometer.isAvailableAsync().catch(() => false),
      Barometer.isAvailableAsync().catch(() => false),
      LightSensor.isAvailableAsync().catch(() => false)
    ]);

    const sensorNames = ['Pedometer', 'Accelerometer', 'Gyroscope', 'Magnetometer', 'Barometer', 'LightSensor'];
    result.availableSensors = sensorNames.filter((_, index) => sensorChecks[index]);

    // Method 1: Try react-native-permissions (latest)
    try {
      const permissionsToCheck = [];
      
      if (Platform.Version >= 35) {
        // Android 16+ health permissions
        if (PERMISSIONS.ANDROID.BODY_SENSORS) permissionsToCheck.push(PERMISSIONS.ANDROID.BODY_SENSORS);
      } else {
        // Traditional permissions
        if (PERMISSIONS.ANDROID.BODY_SENSORS) permissionsToCheck.push(PERMISSIONS.ANDROID.BODY_SENSORS);
        if (Platform.Version >= 29 && PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION) {
          permissionsToCheck.push(PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION);
        }
      }

      if (permissionsToCheck.length > 0) {
        const statuses = await Promise.all(permissionsToCheck.map(p => check(p)));
        const hasGranted = statuses.some(status => status === RESULTS.GRANTED);
        
        if (hasGranted) {
          result.granted = true;
          result.method = 'react-native-permissions';
          result.grantedPermissions = permissionsToCheck.filter((_, index) => statuses[index] === RESULTS.GRANTED);
          return result;
        }
      }
    } catch (error) {
      console.log('⚠️ react-native-permissions check failed:', error);
    }

    // Method 2: Try native PermissionsAndroid
    try {
      const nativePermissions = [PermissionsAndroid.PERMISSIONS.BODY_SENSORS];
      if (Platform.Version >= 29) {
        nativePermissions.push(PermissionsAndroid.PERMISSIONS.ACTIVITY_RECOGNITION);
      }

      const results = await Promise.all(
        nativePermissions.map(p => PermissionsAndroid.check(p))
      );
      
      const hasGranted = results.some(granted => granted);
      if (hasGranted) {
        result.granted = true;
        result.method = 'native-android';
        result.grantedPermissions = nativePermissions.filter((_, index) => results[index]);
        return result;
      }
    } catch (error) {
      console.log('⚠️ Native Android permission check failed:', error);
    }

    // Method 3: Try Expo sensor permissions
    try {
      if (sensorChecks[0]) { // Pedometer available
        const pedometerStatus = await Pedometer.getPermissionsAsync();
        if (pedometerStatus.status === 'granted') {
          result.granted = true;
          result.method = 'expo-pedometer';
          result.grantedPermissions = ['expo-pedometer'];
          return result;
        }
      }
    } catch (error) {
      console.log('⚠️ Expo sensor permission check failed:', error);
    }

    return result;
  }

  /**
   * Request health sensor permissions using multiple methods
   */
  static async requestHealthSensorPermissions(): Promise<PermissionResult> {
    const result: PermissionResult = {
      granted: false,
      method: 'none',
      androidVersion: Platform.Version as number,
      availableSensors: [],
      grantedPermissions: []
    };

    if (Platform.OS !== 'android') {
      result.granted = true;
      result.method = 'ios';
      return result;
    }

    console.log(`🔄 Requesting health permissions for Android ${Platform.Version}`);

    // Check current status first
    const currentStatus = await this.checkHealthSensorPermissions();
    if (currentStatus.granted) {
      return currentStatus;
    }

    result.availableSensors = currentStatus.availableSensors;

    // Method 1: Try react-native-permissions
    try {
      const permissionsToRequest = [];
      
      if (Platform.Version >= 35) {
        // Android 16+ permissions
        if (PERMISSIONS.ANDROID.BODY_SENSORS) permissionsToRequest.push(PERMISSIONS.ANDROID.BODY_SENSORS);
      } else {
        // Traditional permissions
        if (PERMISSIONS.ANDROID.BODY_SENSORS) permissionsToRequest.push(PERMISSIONS.ANDROID.BODY_SENSORS);
        if (Platform.Version >= 29 && PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION) {
          permissionsToRequest.push(PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION);
        }
      }

      if (permissionsToRequest.length > 0) {
        const results = await requestMultiple(permissionsToRequest);
        const hasGranted = Object.values(results).some(status => status === RESULTS.GRANTED);
        
        if (hasGranted) {
          result.granted = true;
          result.method = 'react-native-permissions';
          result.grantedPermissions = Object.keys(results).filter(key => results[key] === RESULTS.GRANTED);
          return result;
        }
      }
    } catch (error) {
      console.log('⚠️ react-native-permissions request failed:', error);
    }

    // Method 2: Try native PermissionsAndroid
    try {
      const nativePermissions = [PermissionsAndroid.PERMISSIONS.BODY_SENSORS];
      if (Platform.Version >= 29) {
        nativePermissions.push(PermissionsAndroid.PERMISSIONS.ACTIVITY_RECOGNITION);
      }

      const results = await PermissionsAndroid.requestMultiple(nativePermissions);
      const hasGranted = Object.values(results).some(status => status === PermissionsAndroid.RESULTS.GRANTED);
      
      if (hasGranted) {
        result.granted = true;
        result.method = 'native-android';
        result.grantedPermissions = Object.keys(results).filter(key => results[key] === PermissionsAndroid.RESULTS.GRANTED);
        return result;
      }
    } catch (error) {
      console.log('⚠️ Native Android permission request failed:', error);
    }

    // Method 3: Try Expo sensors
    try {
      if (result.availableSensors.includes('Pedometer')) {
        const pedometerResult = await Pedometer.requestPermissionsAsync();
        if (pedometerResult.status === 'granted') {
          result.granted = true;
          result.method = 'expo-pedometer';
          result.grantedPermissions = ['expo-pedometer'];
          return result;
        }
      }
    } catch (error) {
      console.log('⚠️ Expo sensor permission request failed:', error);
    }

    return result;
  }

  /**
   * Show permission denied dialog with settings option
   */
  static showPermissionDeniedDialog(permissionType: string, androidVersion: number): void {
    Alert.alert(
      `${permissionType} Permission Required`,
      `${permissionType} permissions are required for health monitoring. Please enable them in Settings.\n\nAndroid ${androidVersion} detected - multiple permission methods were attempted.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Open Settings', 
          onPress: () => openSettings().catch(() => Linking.openSettings())
        }
      ]
    );
  }

  /**
   * Get permission status summary for debugging
   */
  static async getPermissionStatusSummary(): Promise<string> {
    const healthStatus = await this.checkHealthSensorPermissions();
    
    return `
Android Version: ${Platform.Version}
Health Permissions: ${healthStatus.granted ? 'GRANTED' : 'DENIED'}
Method Used: ${healthStatus.method}
Available Sensors: ${healthStatus.availableSensors.join(', ')}
Granted Permissions: ${healthStatus.grantedPermissions.join(', ')}
    `.trim();
  }
}
